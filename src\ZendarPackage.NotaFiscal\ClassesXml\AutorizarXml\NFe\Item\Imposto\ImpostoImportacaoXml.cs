﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto
{
    [XmlRoot(ElementName = "II")]
    public class ImpostoImportacaoXml
    {
        [XmlElement(ElementName = "vBC")]
        public string ValorBaseCalculo { get; set; }

        [XmlElement(ElementName = "vDespAdu")]
        public string ValorDespesasAduaneiras { get; set; }

        [XmlElement(ElementName = "vII")]
        public string ValorImpostoImportacao { get; set; }

        [XmlElement(ElementName = "vIOF")]
        public string ValorIOF { get; set; }

        public static ImpostoImportacaoXml ConverterXml(decimal valorTotal, decimal impostoImportacao)
        {
            if (impostoImportacao == 0)
            {
                return null;
            }

            return new ImpostoImportacaoXml
            {
                ValorBaseCalculo = FormatarValor.FormatarValorXmlNotaFiscal(valorTotal),
                ValorDespesasAduaneiras = FormatarValor.FormatarValorXmlNotaFiscal(0),
                ValorImpostoImportacao = FormatarValor.FormatarValorXmlNotaFiscal(impostoImportacao),
                ValorIOF = FormatarValor.FormatarValorXmlNotaFiscal(0)
            };
        }
    }
}
