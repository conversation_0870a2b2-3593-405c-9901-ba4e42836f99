﻿using System.Collections.Generic;
using System.Linq;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Icms
{
    [XmlRoot(ElementName = "ICMS60")]
    public class Icms60Xml
    {
        [XmlElement(ElementName = "orig")]
        public int Origem { get; set; }

        [XmlElement(ElementName = "CST")]
        public int Cst { get; set; }

        [XmlElement(ElementName = "vBCSTRet")]
        public string ValorBaseCalculoStRetido { get; set; }

        [XmlElement(ElementName = "pST")]
        public string AliquotaConsumidorFinal { get; set; }

        [XmlElement(ElementName = "vICMSSubstituto")]
        public string ValorIcmsSubstituto { get; set; }

        [XmlElement(ElementName = "vICMSSTRet")]
        public string ValorIcmsStRetido { get; set; }

        [XmlElement(ElementName = "vBCFCPSTRet")]
        public string ValorBaseCalculoFCPStRetido { get; set; }

        [XmlElement(ElementName = "pFCPSTRet")]
        public string PercentualFCPStRetido { get; set; }

        [XmlElement(ElementName = "vFCPSTRet")]
        public string ValorFCPStRetido { get; set; }

        public static Icms60Xml ConverterXml(Classes.Autorizar.Item item, ModeloFiscal modelo, List<string> regrasAtivas)
        {
            var icmsXml = new Icms60Xml
            {
                Origem = (int)item.CstOrigem,
                Cst = (int)item.CstCsosn,
                ValorIcmsSubstituto = modelo == ModeloFiscal.NFe
                                      ? FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsSubstituto)
                                      : null
            };

            //se a regra N12-81 estiver ativa, os campos devem ser preenchidos independente do valor
            if (regrasAtivas.FirstOrDefault(r => r == "N12-81") != null)
            {
                icmsXml.ValorBaseCalculoStRetido = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStRetidoBaseCalculo);
                icmsXml.ValorIcmsStRetido = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStRetidoValor);
                icmsXml.AliquotaConsumidorFinal = FormatarValor.FormatarValorXmlNotaFiscal(item.AliquotaConsumidorFinal);
            }
            else
            {
                icmsXml.ValorBaseCalculoStRetido = item.IcmsStRetidoBaseCalculo > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStRetidoBaseCalculo) : null;
                icmsXml.ValorIcmsStRetido = item.IcmsStRetidoValor > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStRetidoValor) : null;
                icmsXml.AliquotaConsumidorFinal = item.AliquotaConsumidorFinal > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.AliquotaConsumidorFinal) : null;
            }

            if (item.FcpStRetidoAliquota > 0)
            {
                icmsXml.ValorBaseCalculoFCPStRetido = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStRetidoBaseCalculo);
                icmsXml.PercentualFCPStRetido = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStRetidoAliquota);
                icmsXml.ValorFCPStRetido = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStRetidoValor);
            }

            return icmsXml;
        }
    }
}
