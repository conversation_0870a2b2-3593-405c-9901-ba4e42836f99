﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.StatusServicoXml
{
    [XmlRoot(ElementName = "consStatServ", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class EnvioStatusServicoXml
    {
        [XmlAttribute("versao")]
        public string Versao { get; set; }

        [XmlElement(ElementName = "tpAmb", Order = 1, IsNullable = false)]
        public int TipoAmbiente { get; set; }

        [XmlElement(ElementName = "cUF", Order = 2, IsNullable = false)]
        public string CodigoUF { get; set; }

        [XmlElement(ElementName = "xServ", Order = 3, IsNullable = false)]
        public string ServicoSolicitado = "STATUS";
    }
}
