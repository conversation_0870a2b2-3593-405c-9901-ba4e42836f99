﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Produto
{
    [XmlRoot(ElementName = "med")]
    public class MedicamentoXml
    {
        [XmlElement(ElementName = "nLote")]
        public string NumeroLote { get; set; }

        [XmlElement(ElementName = "qLote")]
        public string Quantidade { get; set; }

        [XmlElement(ElementName = "dFab")]
        public string DataFabricacao { get; set; }

        [XmlElement(ElementName = "dVal")]
        public string DataValidade { get; set; }

        [XmlElement(ElementName = "vPMC")]
        public string ValorMaximoConsumidor { get; set; }
    }
}
