﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Emitente
{
    [XmlRoot(ElementName = "avulsa")]
    public class IdentificacaoFiscoEmitenteXml
    {
        [XmlElement(ElementName = "CNPJ")]
        public string Cnpj { get; set; }

        [XmlElement(ElementName = "xOrgao")]
        public string OrgaoEmitente { get; set; }

        [XmlElement(ElementName = "matr")]
        public string MatriculaDoAgente { get; set; }

        [XmlElement(ElementName = "xAgente")]
        public string NomeDoAgente { get; set; }

        [XmlElement(ElementName = "fone")]
        public string Telefone { get; set; }

        [XmlElement(ElementName = "UF")]
        public string Uf { get; set; }

        [XmlElement(ElementName = "nDAR")]
        public string NumeroDocumento { get; set; }

        [XmlElement(ElementName = "dEmi")]
        public string DataEmissao { get; set; }

        [XmlElement(ElementName = "vDAR")]
        public string ValorTotal { get; set; }

        [XmlElement(ElementName = "repEmi")]
        public string ReparticaoFical { get; set; }

        [XmlElement(ElementName = "dPag")]
        public string DataPagamento { get; set; }
    }
}
