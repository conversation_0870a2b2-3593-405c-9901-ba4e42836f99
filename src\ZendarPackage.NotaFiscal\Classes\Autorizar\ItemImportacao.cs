﻿using System;
using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.Classes.Autorizar
{
    public class ItemImportacao
    {
        public string NumeroDocumento { get; set; }
        public DateTime DataRegistro { get; set; }
        public string LocalDesembaraco { get; set; }
        public string UfDesembaraco { get; set; }
        public DateTime DataDesembaraco { get; set; }
        public ViaTransporte ViaTransporte { get; set; }
        public FormaImportacao FormaImportacao { get; set; }
        public string CodigoExportador { get; set; }
        public decimal ValorAfrmm { get; set; }
        public string CnpjAdquirente { get; set; }
        public string UfAdquirente { get; set; }
    }
}
