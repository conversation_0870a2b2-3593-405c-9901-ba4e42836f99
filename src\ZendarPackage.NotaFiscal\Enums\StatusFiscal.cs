﻿using System.ComponentModel;

namespace ZendarPackage.NotaFiscal.Enums
{
    public enum StatusFiscal
    {
        [Description("Retorno indisponível")]
        RETORNO_INDISPONIVEL = 0,

        [Description("Em Digitação")]
        EM_DIGITACAO = 1,

        [Description("Em Processamento")]
        EM_PROCESSAMENTO = 2,

        [Description("Rejeitada")]
        REJEITADA = 3,

        [Description("Autorizada")]
        AUTORIZADA = 4,

        [Description("Cancelada")]
        CANCELADA = 5,

        [Description("Carta de Correção")]
        CARTA_CORRECAO = 6,

        [Description("Uso Denegado")]
        USO_DENEGADO = 7,

        [Description("Inutilizada")]
        INUTILIZADA = 8
    }
}
