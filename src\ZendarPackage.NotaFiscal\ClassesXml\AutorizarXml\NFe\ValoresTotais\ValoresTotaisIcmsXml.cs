﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Classes.Autorizar;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.ValoresTotais
{
    [XmlRoot(ElementName = "ICMSTot")]
    public class ValoresTotaisIcmsXml
    {
        [XmlElement(ElementName = "vBC")]
        public string ValorBaseCalculo { get; set; }

        [XmlElement(ElementName = "vICMS")]
        public string ValorICMS { get; set; }

        [XmlElement(ElementName = "vICMSDeson")]
        public string ValorICMSDesonerado { get; set; }

        [XmlElement(ElementName = "vFCPUFDest")]
        public string ValorFcpDestino { get; set; }

        [XmlElement(ElementName = "vICMSUFDest")]
        public string ValorIcmsFcpDestino { get; set; }

        [XmlElement(ElementName = "vICMSUFRemet")]
        public string ValorIcmsFcpRemetente { get; set; }

        [XmlElement(ElementName = "vFCP")]
        public string ValorFCP { get; set; }

        [XmlElement(ElementName = "vBCST")]
        public string ValorBaseCalculoIcmsSt { get; set; }

        [XmlElement(ElementName = "vST")]
        public string ValorICMSST { get; set; }

        [XmlElement(ElementName = "vFCPST")]
        public string ValorFCPST { get; set; }

        [XmlElement(ElementName = "vFCPSTRet")]
        public string ValorFCPSTRet { get; set; }

        [XmlElement(ElementName = "qBCMonoRet")]
        public string QuantidadeBcMonoRetido { get; set; }

        [XmlElement(ElementName = "vICMSMonoRet")]
        public string ValorIcmsMonoRetido { get; set; }

        [XmlElement(ElementName = "vProd")]
        public string ValorProduto { get; set; }

        [XmlElement(ElementName = "vFrete")]
        public string ValorFrete { get; set; }

        [XmlElement(ElementName = "vSeg")]
        public string ValorSeguro { get; set; }

        [XmlElement(ElementName = "vDesc")]
        public string ValorDesconto { get; set; }

        [XmlElement(ElementName = "vII")]
        public string ValorII { get; set; }

        [XmlElement(ElementName = "vIPI")]
        public string ValorIPI { get; set; }

        [XmlElement(ElementName = "vIPIDevol")]
        public string ValorIPIDevol { get; set; }

        [XmlElement(ElementName = "vPIS")]
        public string ValorPIS { get; set; }

        [XmlElement(ElementName = "vCOFINS")]
        public string ValorCOFINS { get; set; }

        [XmlElement(ElementName = "vOutro")]
        public string ValorOutrasDespesas { get; set; }

        [XmlElement(ElementName = "vNF")]
        public string ValorNFe { get; set; }

        [XmlElement(ElementName = "vTotTrib")]
        public string ValorTotalTributos { get; set; }



        public static ValoresTotaisIcmsXml ConverterXml(Totais totais, FinalidadeNotaFiscal finalidade, bool possuiItens)
        {
            if (totais is null) return null;

            var totaisIcmsXml = new ValoresTotaisIcmsXml()
            {
                ValorBaseCalculo = FormatarValor.FormatarValorXmlNotaFiscal(totais.IcmsBaseCalculo),
                ValorICMS = FormatarValor.FormatarValorXmlNotaFiscal(totais.IcmsValor),
                ValorICMSDesonerado = FormatarValor.FormatarValorXmlNotaFiscal(totais.IcmsDesoneradoValorTotal),
                ValorFCP = FormatarValor.FormatarValorXmlNotaFiscal(totais.IcmsFcpValorTotal),
                ValorBaseCalculoIcmsSt = FormatarValor.FormatarValorXmlNotaFiscal(totais.IcmsStBaseCalculo),
                ValorICMSST = FormatarValor.FormatarValorXmlNotaFiscal(totais.IcmsStValor),
                ValorFCPST = FormatarValor.FormatarValorXmlNotaFiscal(totais.IcmsStFcpValorTotal),
                ValorFCPSTRet = FormatarValor.FormatarValorXmlNotaFiscal(totais.IcmsStFcpRetidoValorTotal),
                ValorProduto = FormatarValor.FormatarValorXmlNotaFiscal(totais.ValorProdutos),
                ValorFrete = FormatarValor.FormatarValorXmlNotaFiscal(totais.ValorFrete),
                ValorSeguro = FormatarValor.FormatarValorXmlNotaFiscal(totais.ValorSeguro),
                ValorDesconto = FormatarValor.FormatarValorXmlNotaFiscal(totais.ValorDesconto),
                ValorII = FormatarValor.FormatarValorXmlNotaFiscal(totais.ValorImpostoImportacao),
                ValorIPI = FormatarValor.FormatarValorXmlNotaFiscal(totais.ValorIpi),
                ValorIPIDevol = FormatarValor.FormatarValorXmlNotaFiscal(totais.IpiDevolucaoValorTotal),
                ValorPIS = FormatarValor.FormatarValorXmlNotaFiscal(totais.PisValorTotal),
                ValorCOFINS = FormatarValor.FormatarValorXmlNotaFiscal(totais.CofinsValorTotal),
                ValorOutrasDespesas = FormatarValor.FormatarValorXmlNotaFiscal(totais.ValorDespesas),
                ValorNFe = FormatarValor.FormatarValorXmlNotaFiscal(totais.ValorTotal),
                ValorTotalTributos = FormatarValor.FormatarValorXmlNotaFiscal(totais.ValorTotalTributos),
                ValorIcmsMonoRetido = FormatarValor.FormatarValorXmlNotaFiscal(totais.ValorIcmsMonoRetido),
                QuantidadeBcMonoRetido = FormatarValor.FormatarValorXmlNotaFiscal(totais.QuantidadeBcMonoRetido),
            };

            if (totais.IcmsFcpDestinoValorTotal > 0)
            {
                totaisIcmsXml.ValorFcpDestino = FormatarValor.FormatarValorXmlNotaFiscal(totais.FcpUfDestinoValorTotal);
                totaisIcmsXml.ValorIcmsFcpDestino = FormatarValor.FormatarValorXmlNotaFiscal(totais.IcmsFcpDestinoValorTotal);
                totaisIcmsXml.ValorIcmsFcpRemetente = FormatarValor.FormatarValorXmlNotaFiscal(totais.IcmsFcpRemetenteValorTotal);
            }

            return totaisIcmsXml;
        }
    }
}
