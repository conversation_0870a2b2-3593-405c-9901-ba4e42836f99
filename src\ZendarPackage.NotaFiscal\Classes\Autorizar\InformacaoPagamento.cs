﻿using System;
using ZendarPackage.NotaFiscal.Helpers.Validacoes;

namespace ZendarPackage.NotaFiscal.Classes.Autorizar
{
	public class InformacaoPagamento
    {
		public InformacaoPagamento(string cnpjPagamento, string ufPagamento)
		{
			AssegureInformacoesValidasDoPagamento(cnpjPagamento, ufPagamento);

			CnpjPagamento = cnpjPagamento;
			UfPagamento = ufPagamento;
		}

		public string CnpjPagamento { get; set; }
        public string UfPagamento { get; set; }

		private void AssegureInformacoesValidasDoPagamento(string cnpjPagamento, string ufPagamento)
		{
			if (string.IsNullOrEmpty(cnpjPagamento))
				throw new ArgumentNullException(nameof(cnpjPagamento));

			if (!ValidacaoCpfCnpj.ValidarCnpj(cnpjPagamento))
				throw new ArgumentException("Invalid Payment CNPJ.");

			if (string.IsNullOrEmpty(ufPagamento))
				throw new ArgumentNullException(nameof(ufPagamento));
		}
    }
}
