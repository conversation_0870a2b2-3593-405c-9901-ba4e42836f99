﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.EventoXml.Retorno
{
    public class RetornoEventoInformacoesXml
    {
        [XmlAttribute("Id")]
        public string Id { get; set; }

        [XmlElement(ElementName = "tpAmb", Order = 1, IsNullable = false)]
        public int TipoAmbiente { get; set; }

        [XmlElement(ElementName = "verAplic", Order = 2, IsNullable = false)]
        public string VersaoAplicacao { get; set; }

        [XmlElement(ElementName = "cOrgao", Order = 3, IsNullable = false)]
        public int CodigoOrgao { get; set; }

        [XmlElement(ElementName = "cStat", Order = 4, IsNullable = false)]
        public int CodigoStatus { get; set; }

        [XmlElement(ElementName = "xMotivo", Order = 5, IsNullable = false)]
        public string Motivo { get; set; }

        [XmlElement(ElementName = "chNFe", Order = 6, IsNullable = false)]
        public string ChaveAcesso { get; set; }

        [XmlElement(ElementName = "tpEvento", Order = 7, IsNullable = false)]
        public int TipoEvento { get; set; }

        [XmlElement(ElementName = "xEvento", Order = 8, IsNullable = false)]
        public string DescricaoEvento { get; set; }

        [XmlElement(ElementName = "nSeqEvento", Order = 9, IsNullable = false)]
        public string NumeroSequencial { get; set; }

        [XmlElement(ElementName = "CNPJDest", Order = 10, IsNullable = false)]
        public string CnpjDestinatario { get; set; }

        [XmlElement(ElementName = "CPFDest", Order = 11, IsNullable = false)]
        public string CpfDestinatario { get; set; }

        [XmlElement(ElementName = "emailDest", Order = 12, IsNullable = false)]
        public string EmailDestinatario { get; set; }

        [XmlElement(ElementName = "dhRegEvento", Order = 13, IsNullable = false)]
        public string DataHoraRegistroEvento { get; set; }

        [XmlElement(ElementName = "nProt", Order = 14, IsNullable = false)]
        public string NumeroProtocolo { get; set; }
    }
}
