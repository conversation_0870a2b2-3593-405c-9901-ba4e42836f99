﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.InutilizarXml.Envio
{
    [XmlRoot(ElementName = "infInut", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class EnvioInutilizarInformacoesXml
    {
        [XmlAttribute("Id")]
        public string Id { get; set; }

        [XmlElement(ElementName = "tpAmb", Order = 1, IsNullable = false)]
        public int TipoAmbiente { get; set; }

        [XmlElement(ElementName = "xServ", Order = 2, IsNullable = false)]
        public string ServicoSolicitado = "INUTILIZAR";

        [XmlElement(ElementName = "cUF", Order = 3, IsNullable = false)]
        public int CodigoUF { get; set; }

        [XmlElement(ElementName = "ano", Order = 4, IsNullable = false)]
        public int Ano { get; set; }

        [XmlElement(ElementName = "CNPJ", Order = 5, IsNullable = false)]
        public string Cnpj { get; set; }

        [XmlElement(ElementName = "mod", Order = 6, IsNullable = false)]
        public int Modelo { get; set; }

        [XmlElement(ElementName = "serie", Order = 7, IsNullable = false)]
        public int Serie { get; set; }

        [XmlElement(ElementName = "nNFIni", Order = 8, IsNullable = false)]
        public int NumeroNFeInicial { get; set; }

        [XmlElement(ElementName = "nNFFin", Order = 9, IsNullable = false)]
        public int NumeroNFeFinal { get; set; }

        [XmlElement(ElementName = "xJust", Order = 10, IsNullable = false)]
        public string Justificativa { get; set; }
    }
}
