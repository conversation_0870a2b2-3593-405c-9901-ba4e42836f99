﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.ClassesXml.EventoXml.Retorno;

namespace ZendarPackage.NotaFiscal.ClassesXml.EventoXml.Evento
{
    [XmlRoot(ElementName = "procEventoNFe", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class EventoProcessadoXml
    {
        [XmlAttribute("versao")]
        public string Versao { get; set; }

        [XmlElement(ElementName = "evento", Order = 2, IsNullable = false)]
        public EventoXml EnvioEvento { get; set; }

        [XmlElement(ElementName = "retEvento", Order = 3, IsNullable = false)]
        public RetornoEventoXml RetornoEvento { get; set; }
    }
}
