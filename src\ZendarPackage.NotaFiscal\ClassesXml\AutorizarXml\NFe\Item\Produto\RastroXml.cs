﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Produto
{
    [XmlRoot(ElementName = "rastro")]
    public class RastroXml
    {
        [XmlElement(ElementName = "nLote")]
        public string NumeroLote { get; set; }

        [XmlElement(ElementName = "qLote")]
        public string QuantidadeLote { get; set; }

        [XmlElement(ElementName = "dFab")]
        public string DataFabricacao { get; set; }

        [XmlElement(ElementName = "dVal")]
        public string DataValidade { get; set; }

        [XmlElement(ElementName = "cAgreg")]
        public int CodigoAgregacao { get; set; }
    }
}
