﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto
{
    [XmlRoot(ElementName = "ICMSUFDest")]
    public class IcmsUfDestinoXml
    {
        [XmlElement(ElementName = "vBCUFDest")]
        public string ValorBaseCalculoUfDestino { get; set; }

        [XmlElement(ElementName = "vBCFCPUFDest")]
        public string ValorBaseCalculoFCPUfDestino { get; set; }

        [XmlElement(ElementName = "pFCPUFDest")]
        public string PercentualFCPUfDestino { get; set; }

        [XmlElement(ElementName = "pICMSUFDest")]
        public string AliquotaInternaUfDestino { get; set; }

        [XmlElement(ElementName = "pICMSInter")]
        public string AliquotaInterestadualUfEnvolvida { get; set; }

        [XmlElement(ElementName = "pICMSInterPart")]
        public string PercentualPartilhaIcms { get; set; }

        [XmlElement(ElementName = "vFCPUFDest")]
        public string ValorFCPUfDestino { get; set; }

        [XmlElement(ElementName = "vICMSUFDest")]
        public string ValorIcmsInterestadual { get; set; }

        [XmlElement(ElementName = "vICMSUFRemet")]
        public string ValorIcmsRemetente { get; set; }

        public static IcmsUfDestinoXml ConverterXml(Classes.Autorizar.Item item)
        {
            return new IcmsUfDestinoXml
            {
                ValorBaseCalculoUfDestino = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsUfDestinoBaseCalculo),
                ValorBaseCalculoFCPUfDestino = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpUfDestinoBaseCalculo),
                PercentualFCPUfDestino = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpUfDestinoPercentual),
                AliquotaInternaUfDestino = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsUfDestinoPercentual),
                AliquotaInterestadualUfEnvolvida = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsAliquotaInterestadual),
                PercentualPartilhaIcms = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsPercentualPartilha),
                ValorFCPUfDestino = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpUfDestinoValor),
                ValorIcmsInterestadual = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsUfDestinoValor),
                ValorIcmsRemetente = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsUfRemetenteValor)
            };
        }
    }
}
