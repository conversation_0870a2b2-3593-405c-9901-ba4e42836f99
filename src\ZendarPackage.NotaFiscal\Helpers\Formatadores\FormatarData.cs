﻿using System;

namespace ZendarPackage.NotaFiscal.Helpers.Formatadores
{
    internal static class FormatarData
    {
        public static string FormatarDataXmlNotaFiscal(DateTime? data, int timezoneOffset)
        {
            if (data.HasValue)
            {
                var dataComTimezone = data.Value.AddHours(timezoneOffset);

                var diferenca = dataComTimezone - data.Value;
                var diferencaHoras = $"{(timezoneOffset > 0 ? "+" : "-")}{diferenca:hh\\:mm}";

                return $"{data.Value:yyyy'-'MM'-'dd'T'HH':'mm':'ss}{diferencaHoras}";
            }

            return null;
        }

        public static string ToDateOnlyXmlNotaFiscal(DateTime? data)
        {
            if (!data.HasValue) return null;

			return data.Value.ToString("yyyy-MM-dd");
		}
    }
}
