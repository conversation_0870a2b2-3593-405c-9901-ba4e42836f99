﻿using System;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Classes.Autorizar;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Identificacao
{
    [XmlRoot(ElementName = "ide")]
    public class IdentificacaoNFeXml
    {
        [XmlElement(ElementName = "cUF")]
        public int CodigoUF { get; set; }

        [XmlElement(ElementName = "cNF")]
        public int CodigoNumericoAleatorio { get; set; }

        [XmlElement(ElementName = "natOp")]
        public string NaturezaOperacao { get; set; }

        [XmlElement(ElementName = "mod")]
        public int Modelo { get; set; }

        [XmlElement(ElementName = "serie")]
        public int Serie { get; set; }

        [XmlElement(ElementName = "nNF")]
        public int Numero { get; set; }

        [XmlElement(ElementName = "dhEmi")]
        public string DataEmissao { get; set; }

        [XmlElement(ElementName = "dhSaiEnt")]
        public string DataSaidaEntrada { get; set; }

        [XmlElement(ElementName = "tpNF")]
        public int TipoOperacao { get; set; }

        [XmlElement(ElementName = "idDest")]
        public int IdentificadorDestinoOperacao { get; set; }

        [XmlElement(ElementName = "cMunFG")]
        public string CodigoMunicipioGerador { get; set; }

        [XmlElement(ElementName = "tpImp")]
        public int TipoImpressao { get; set; }

        [XmlElement(ElementName = "tpEmis")]
        public int TipoEmissao { get; set; }

        [XmlElement(ElementName = "cDV")]
        public int DigitoVerificador { get; set; }

        [XmlElement(ElementName = "tpAmb")]
        public int TipoAmbiente { get; set; }

        [XmlElement(ElementName = "finNFe")]
        public int FinalidadeEmissao { get; set; }

        [XmlElement(ElementName = "indFinal")]
        public int IndicaOperacaoConsumidorFinal { get; set; }

        [XmlElement(ElementName = "indPres")]
        public int IndicadorPresencaComprador { get; set; }

        [XmlElement(ElementName = "indIntermed")]
        public string IndicadorIntermediador { get; set; } = null;

        [XmlElement(ElementName = "procEmi")]
        public int ProcessoEmissao { get; set; }

        [XmlElement(ElementName = "verProc")]
        public string VersaoProcesso { get; set; }

        [XmlElement(ElementName = "dhCont")]
        public string DataHoraEntradaContingencia { get; set; }

        [XmlElement(ElementName = "xJust")]
        public string JustificativaEntradaContingencia { get; set; }

        [XmlElement(ElementName = "NFref")]
        public GrupoNotaFiscalReferenciadaXml[] NotasFiscaisReferenciadasXml { get; set; }

        public static IdentificacaoNFeXml ConverterXml(InformacoesAutorizacao informacoesAutorizacao)
        {
            var ideXml = new IdentificacaoNFeXml
            {
                CodigoUF = informacoesAutorizacao.Emitente.CodigoUf,
                CodigoNumericoAleatorio = informacoesAutorizacao.CodigoNf,
                NaturezaOperacao = FormatarTexto.RemoverEspacos(informacoesAutorizacao.NaturezaOperacao),
                Modelo = (int)informacoesAutorizacao.ModeloFiscal,
                Serie = informacoesAutorizacao.Serie,
                Numero = informacoesAutorizacao.Numero,
                DataEmissao = FormatarData.FormatarDataXmlNotaFiscal(informacoesAutorizacao.DataEmissao, informacoesAutorizacao.TimeZoneOffset),
                TipoOperacao = (int)informacoesAutorizacao.TipoOperacao,
                IdentificadorDestinoOperacao = (int)informacoesAutorizacao.IdentificadorDestinoOperacao,
                CodigoMunicipioGerador = FormatarTexto.RemoverEspacos(informacoesAutorizacao.Emitente.CodigoIbge),
                TipoEmissao = (int)informacoesAutorizacao.TipoEmissao,
                DigitoVerificador = Convert.ToInt32(informacoesAutorizacao.ChaveAcesso.Substring(informacoesAutorizacao.ChaveAcesso.Length - 1, 1)),
                TipoAmbiente = (int)informacoesAutorizacao.AmbienteFiscal,
                FinalidadeEmissao = (int)informacoesAutorizacao.Finalidade,
                IndicaOperacaoConsumidorFinal = (int)informacoesAutorizacao.IndicaOperacaoConsumidorFinal,
                IndicadorPresencaComprador = (int)informacoesAutorizacao.IndicadorPresencaComprador,
                ProcessoEmissao = 0, //Emissão de NF-e com aplicativo do contribuinte;
                VersaoProcesso = "1.0", //versão do aplicativo emissor de NF-e
                NotasFiscaisReferenciadasXml = GrupoNotaFiscalReferenciadaXml.ConverterXml(informacoesAutorizacao)
            };

            if (informacoesAutorizacao.IndicadorPresencaComprador != OperacaoPresencaComprador.NAO_SE_APLICA &&
                informacoesAutorizacao.IndicadorPresencaComprador != OperacaoPresencaComprador.OPERACAO_PRESENCIAL &&
                informacoesAutorizacao.IndicadorPresencaComprador != OperacaoPresencaComprador.OPERACAO_PRESENCIAL_FORA_ESTABELECIMENTO)
            {
                ideXml.IndicadorIntermediador = Convert.ToInt32(informacoesAutorizacao.OperacaoComIntermediador).ToString();
            }
             
            if (informacoesAutorizacao.ModeloFiscal == ModeloFiscal.NFe)
            {
                ideXml.DataSaidaEntrada = FormatarData.FormatarDataXmlNotaFiscal(informacoesAutorizacao.DataSaidaEntrada, informacoesAutorizacao.TimeZoneOffset);
                ideXml.TipoImpressao = (int)TipoImpressaoFiscal.DANFE_RETRATO;
            }
            else
            {
                ideXml.TipoImpressao = (int)TipoImpressaoFiscal.DANFE_NFCE;
            }

            if (informacoesAutorizacao.TipoEmissao != FormaEmissaoFiscal.NORMAL)
            {
                ideXml.DataHoraEntradaContingencia = ideXml.DataEmissao;
                ideXml.JustificativaEntradaContingencia = "Webservice da SEFAZ nao esta acessivel no momento.";
            }

            return ideXml;
        }
    }
}
