﻿using System.Collections.Generic;
using System.Linq;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Extensions;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Pagamento
{
    [XmlRoot(ElementName = "detPag")]
    public class GrupoFormaPagamentoXml
    {
        [XmlElement(ElementName = "indPag")]
        public int IndPag { get; set; }

        [XmlElement(ElementName = "tPag")]
        public string TipoPagamento { get; set; }

        [XmlElement(ElementName = "vPag")]
        public string ValorPagamento { get; set; }

		[XmlElement(ElementName = "dPag")]
		public string DataPagamento { get; set; }

		#region [Sequencia Opcional]
		[XmlElement(ElementName = "CNPJPag")]
		public string CnpjPagamento { get; set; }

		[XmlElement(ElementName = "UFPag")]
		public string UfPagamento { get; set; }
        #endregion

        [XmlElement(ElementName = "card")]
        public GrupoCartoesXml GrupoCartoes { get; set; }

        public static GrupoFormaPagamentoXml[] ConverterXml(List<Classes.Autorizar.Pagamento> pagamentos)
        {
            var listaGrupoFormaPagtoXml = new List<GrupoFormaPagamentoXml>();

            if (pagamentos.Any())
            {
                foreach (var pagamento in pagamentos)
                {
                    listaGrupoFormaPagtoXml.Add(new GrupoFormaPagamentoXml
                    {
                        IndPag = (int)pagamento.IndicadorPagamento,
                        TipoPagamento = pagamento.MeioPagamento.FormatarValorEnumXmlNotaFiscal(),
                        ValorPagamento = FormatarValor.FormatarValorXmlNotaFiscal(pagamento.Valor + pagamento.Troco),
                        DataPagamento = FormatarData.ToDateOnlyXmlNotaFiscal(pagamento.DataPagamento),
                        CnpjPagamento = FormatarTexto.ManterSomenteNumeros(pagamento.InformacoesPagamento?.CnpjPagamento),
                        UfPagamento = pagamento.InformacoesPagamento?.UfPagamento,
						GrupoCartoes = GrupoCartoesXml.ConverterXml(pagamento)
                    });
                }
            }
            else
            {
                listaGrupoFormaPagtoXml.Add(new GrupoFormaPagamentoXml
                {
                    IndPag = (int)IndicadorPagamento.A_VISTA,
                    TipoPagamento = MeioPagamentoFiscal.SEM_PAGAMENTO.FormatarValorEnumXmlNotaFiscal(),
                    ValorPagamento = FormatarValor.FormatarValorXmlNotaFiscal(0)
                });
            }

            return listaGrupoFormaPagtoXml.ToArray();
        }
    }
}
