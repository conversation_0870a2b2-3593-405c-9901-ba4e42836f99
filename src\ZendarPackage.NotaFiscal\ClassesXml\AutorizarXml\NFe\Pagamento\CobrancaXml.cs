﻿using System.Collections.Generic;
using System.Linq;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Pagamento
{
    [XmlRoot(ElementName = "cobr")]
    public class CobrancaXml
    {
        [XmlElement(ElementName = "fat")]
        public FaturaXml Fatura { get; set; }

        [XmlElement(ElementName = "dup")]
        public DuplicataXml[] Duplicata { get; set; }

        public static CobrancaXml ConverterXml(ModeloFiscal modeloFiscal, decimal valorTotal, List<Classes.Autorizar.Pagamento> pagamentos)
        {
            if (modeloFiscal != ModeloFiscal.NFe || !pagamentos.Any())
            {
                return null;
            }

            return new CobrancaXml
            {
                Fatura = FaturaXml.ConverterXml(valorTotal),
                Duplicata = DuplicataXml.ConverterXml(pagamentos)
            };
        }
    }
}
