﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.ProtocoloXml
{
    [XmlRoot(ElementName = "protNFe", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class RetornoProtocoloXml
    {
        [XmlAttribute("versao")]
        public string Versao { get; set; }

        [XmlElement("infProt", Order = 1, IsNullable = false)]
        public RetornoProtocoloInformacoesXml InformacoesProtocoloXml { get; set; }

        [XmlElement(ElementName = "Signature", Order = 2, IsNullable = false)]
        public AssinaturaXml AssinaturaXml { get; set; }
    }
}
