﻿using System.Collections.Generic;
using System.Linq;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Pagamento
{
    [XmlRoot(ElementName = "pag")]
    public class PagXml
    {
        [XmlElement(ElementName = "detPag")]
        public GrupoFormaPagamentoXml[] GrupoFormaPagamento { get; set; }

        [XmlElement(ElementName = "vTroco")]
        public string VTroco { get; set; }
        public bool VTrocoSpecified
        {
            get { return !string.IsNullOrEmpty(VTroco); }
        }

        public static PagXml ConverterXml(List<Classes.Autorizar.Pagamento> pagamentos)
        {
            var troco = pagamentos.Sum(p => p.Troco);
            return new PagXml
            {
                GrupoFormaPagamento = GrupoFormaPagamentoXml.ConverterXml(pagamentos),
                VTroco = troco > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(troco) : ""
            };
        }
    }
}
