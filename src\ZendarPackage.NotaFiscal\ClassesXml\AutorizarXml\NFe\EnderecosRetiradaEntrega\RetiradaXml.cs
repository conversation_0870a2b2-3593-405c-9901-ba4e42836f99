﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Classes.Autorizar;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.EnderecosRetiradaEntrega
{
    [XmlRoot(ElementName = "retirada")]
    public class RetiradaXml
    {
        [XmlElement(ElementName = "CNPJ")]
        public string Cnpj { get; set; }

        [XmlElement(ElementName = "CPF")]
        public string Cpf { get; set; }

        [XmlElement(ElementName = "xNome")]
        public string Nome { get; set; }

        [XmlElement(ElementName = "xLgr")]
        public string Logradouro { get; set; }

        [XmlElement(ElementName = "nro")]
        public string Numero { get; set; }

        [XmlElement(ElementName = "xCpl")]
        public string Complemento { get; set; }

        [XmlElement(ElementName = "xBairro")]
        public string Bairro { get; set; }

        [XmlElement(ElementName = "cMun")]
        public string CodMunicipio { get; set; }

        [XmlElement(ElementName = "xMun")]
        public string NomeMunicipio { get; set; }

        [XmlElement(ElementName = "UF")]
        public string SiglaUF { get; set; }

        [XmlElement(ElementName = "CEP")]
        public string Cep { get; set; }

        [XmlElement(ElementName = "cPais")]
        public string CodPais { get; set; }

        [XmlElement(ElementName = "xPais")]
        public string NomePais { get; set; }

        [XmlElement(ElementName = "fone")]
        public string Fone { get; set; }

        [XmlElement(ElementName = "email")]
        public string Email { get; set; }

        [XmlElement(ElementName = "IE")]
        public string InscricaoEstadual { get; set; }

        public static RetiradaXml ConverterXml(Endereco enderecoRetirada)
        {
            if (enderecoRetirada == null)
            {
                return null;
            }

            var retiradaXml = new RetiradaXml
            {
                Cnpj = !string.IsNullOrEmpty(enderecoRetirada.Cnpj)
                        ? FormatarTexto.ManterSomenteNumeros(enderecoRetirada.Cnpj)
                        : null,

                Cpf = !string.IsNullOrEmpty(enderecoRetirada.Cpf)
                        ? FormatarTexto.ManterSomenteNumeros(enderecoRetirada.Cpf)
                        : null,

                Nome = FormatarTexto.RemoverEspacos(enderecoRetirada.RazaoSocial),
                Email = FormatarTexto.RemoverEspacos(enderecoRetirada.Email),
                InscricaoEstadual = FormatarTexto.ManterSomenteNumeros(enderecoRetirada.InscricaoEstadual),
                Logradouro = FormatarTexto.ManterSomenteNumerosELetras(enderecoRetirada.Logradouro),
                Numero = FormatarTexto.ManterSomenteNumerosELetras(enderecoRetirada.Numero),
                Complemento = FormatarTexto.ManterSomenteNumerosELetras(enderecoRetirada.Complemento),
                Bairro = FormatarTexto.ManterSomenteNumerosELetras(enderecoRetirada.Bairro),
                CodMunicipio = FormatarTexto.ManterSomenteNumeros(enderecoRetirada.CodigoIbge),
                NomeMunicipio = FormatarTexto.ManterSomenteNumerosELetras(enderecoRetirada.Cidade),
                SiglaUF = FormatarTexto.ManterSomenteNumerosELetras(enderecoRetirada.SiglaUf),
                Cep = FormatarTexto.ManterSomenteNumeros(enderecoRetirada.Cep),
                CodPais = FormatarTexto.ManterSomenteNumeros(enderecoRetirada.CodigoPais),
                NomePais = FormatarTexto.ManterSomenteNumerosELetras(enderecoRetirada.Pais),
                Fone = FormatarTexto.ManterSomenteNumeros(enderecoRetirada.Telefone)
            };

            return retiradaXml;
        }
    }
}
