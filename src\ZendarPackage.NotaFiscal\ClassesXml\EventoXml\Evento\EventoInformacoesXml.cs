﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.EventoXml.Evento
{
    [XmlRoot(ElementName = "infEvento", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class EventoInformacoesXml
    {
        [XmlAttribute("Id")]
        public string Id { get; set; }

        [XmlElement(ElementName = "cOrgao", Order = 1, IsNullable = false)]
        public int CodigoOrgao { get; set; }

        [XmlElement(ElementName = "tpAmb", Order = 2, IsNullable = false)]
        public int TipoAmbiente { get; set; }

        [XmlElement(ElementName = "CNPJ", Order = 3, IsNullable = false)]
        public string Cnpj { get; set; }

        [XmlElement(ElementName = "CPF", Order = 4, IsNullable = false)]
        public string Cpf { get; set; }

        [XmlElement(ElementName = "chNFe", Order = 5, IsNullable = false)]
        public string ChaveAcesso { get; set; }

        [XmlElement(ElementName = "dhEvento", Order = 6, IsNullable = false)]
        public string DataHoraEvento { get; set; }

        [XmlElement(ElementName = "tpEvento", Order = 7, IsNullable = false)]
        public int TipoEvento { get; set; }

        [XmlElement(ElementName = "nSeqEvento", Order = 8, IsNullable = false)]
        public int NumeroSequencial { get; set; }

        [XmlElement(ElementName = "verEvento", Order = 9, IsNullable = false)]
        public string VersaoEvento { get; set; }

        [XmlElement(ElementName = "detEvento", Order = 10, IsNullable = false)]
        public EventoDetalhesXml DetalhesEventoXml { get; set; }
    }
}
