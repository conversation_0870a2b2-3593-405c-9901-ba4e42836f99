﻿using System.Collections.Generic;
using System.IO;
using System.Xml;
using System.Xml.Schema;
using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.Helpers.ManipuladorXml
{
    public static class ValidarXml
    {
        private static string MsgValidacao = "";

        public static (bool sucesso, string mensagem) Validar(string xml, ServicoNotaFiscal tipoServico, string pacoteLiberacao, string versaoWebservice)
        {
            if (string.IsNullOrEmpty(xml))
            {
                return (false, "Não foi possível validar o xml da nota fiscal.");
            }

            var schemas = PacoteLiberacaoSchemas.PreencherSchemas(tipoServico, pacoteLiberacao, versaoWebservice);

            return (ValidarSchemaXml(xml, schemas), MsgValidacao);
        }

        private static bool ValidarSchemaXml(string xml, Dictionary<string, string> schemas)
        {
            MsgValidacao = "";
            var readerSettings = new XmlReaderSettings();
            foreach (var schema in schemas)
            {
                readerSettings.Schemas.Add(schema.Value, schema.Key);
            }
            readerSettings.ValidationType = ValidationType.Schema;
            readerSettings.ValidationEventHandler += new ValidationEventHandler(Reader_ValidationEventHandler);

            XmlReader books = XmlReader.Create(new XmlTextReader(new StringReader(xml)), readerSettings);

            while (books.Read()) { }

            return string.IsNullOrEmpty(MsgValidacao);
        }

        private static void Reader_ValidationEventHandler(object sender, ValidationEventArgs e)
        {
            MsgValidacao += $" Erro: {e.Exception.Message}";
        }
    }
}
