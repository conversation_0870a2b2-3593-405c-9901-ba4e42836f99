﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;
using ZendarPackage.NotaFiscal.Helpers.Validacoes;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Emitente
{
    [XmlRoot(ElementName = "emit")]
    public class EmitenteXml
    {
        [XmlElement(ElementName = "CNPJ")]
        public string Cnpj { get; set; }

        [XmlElement(ElementName = "CPF")]
        public string Cpf { get; set; }

        [XmlElement(ElementName = "xNome")]
        public string Nome { get; set; }

        [XmlElement(ElementName = "xFant")]
        public string Fantasia { get; set; }

        [XmlElement(ElementName = "enderEmit")]
        public EnderecoEmitenteXml EnderecoEmitente { get; set; }

        [XmlElement(ElementName = "IE")]
        public string Ie { get; set; }

        [XmlElement(ElementName = "IEST")]
        public string Iest { get; set; }

        [XmlElement(ElementName = "IM")]
        public string InscricaoMunicipal { get; set; }

        [XmlElement(ElementName = "CNAE")]
        public string Cnae { get; set; }

        [XmlElement(ElementName = "CRT")]
        public int Crt { get; set; }

        public static EmitenteXml ConverterXml(Classes.Autorizar.Emitente emitente)
        {
            return new EmitenteXml
            {
                Cnpj = ValidacaoCpfCnpj.ValidarCnpj(emitente.Cnpj) 
                     ? FormatarTexto.ManterSomenteNumeros(emitente.Cnpj) 
                     : null,
                Cpf = ValidacaoCpfCnpj.ValidarCpf(emitente.Cnpj) 
                    ? FormatarTexto.ManterSomenteNumeros(emitente.Cnpj)
                    : null,
                Nome = FormatarTexto.ManterSomenteNumerosELetras(emitente.RazaoSocial),
                Fantasia = FormatarTexto.ManterSomenteNumerosELetras(emitente.NomeFantasia),
                EnderecoEmitente = new EnderecoEmitenteXml
                {
                    Logradouro = FormatarTexto.ManterSomenteNumerosELetras(emitente.Logradouro),
                    Numero = FormatarTexto.ManterSomenteNumerosELetras(emitente.Numero),
                    Complemento = FormatarTexto.ManterSomenteNumerosELetras(emitente.Complemento),
                    Bairro = FormatarTexto.ManterSomenteNumerosELetras(emitente.Bairro),
                    CodigoMunicipio = FormatarTexto.ManterSomenteNumeros(emitente.CodigoIbge),
                    NomeMunicipio = FormatarTexto.ManterSomenteNumerosELetras(emitente.Cidade),
                    SiglaUF = FormatarTexto.ManterSomenteNumerosELetras(emitente.SiglaUf),
                    Cep = FormatarTexto.ManterSomenteNumeros(emitente.Cep),
                    CodigoPais = FormatarTexto.ManterSomenteNumeros(emitente.CodigoPais),
                    NomePais = FormatarTexto.ManterSomenteNumerosELetras(emitente.Pais),
                    Telefone = FormatarTexto.ManterSomenteNumeros(emitente.Telefone)
                },
                Ie = FormatarTexto.ManterSomenteNumerosELetras(emitente.InscricaoEstadual),
                Crt = (int)emitente.RegimeTributario
            };
        }
    }
}
