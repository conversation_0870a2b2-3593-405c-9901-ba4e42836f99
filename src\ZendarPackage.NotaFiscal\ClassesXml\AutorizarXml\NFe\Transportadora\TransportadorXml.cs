﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;
using ZendarPackage.NotaFiscal.Helpers.Validacoes;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Transportadora
{
    [XmlRoot(ElementName = "transporta")]
    public class TransportadorXml
    {
        [XmlElement(ElementName = "CNPJ")]
        public string Cnpj { get; set; }

        [XmlElement(ElementName = "CPF")]
        public string Cpf { get; set; }

        [XmlElement(ElementName = "xNome")]
        public string Nome { get; set; }

        [XmlElement(ElementName = "IE")]
        public string Ie { get; set; }

        [XmlElement(ElementName = "xEnder")]
        public string EnderecoCompleto { get; set; }

        [XmlElement(ElementName = "xMun")]
        public string NomeMunicipio { get; set; }

        [XmlElement(ElementName = "UF")]
        public string SiglaUF { get; set; }

        public static TransportadorXml ConverterXml(Classes.Autorizar.Transportadora transportadora)
        {
            string logradouro = transportadora.Logradouro;

            if (!string.IsNullOrEmpty(logradouro) && !string.IsNullOrEmpty(transportadora.Numero))
            {
                logradouro = $"{logradouro}, {transportadora.Numero}";
            }

            var transportadoraXml = new TransportadorXml
            {
                Nome = FormatarTexto.RemoverEspacos(transportadora.Nome),
                Ie = FormatarTexto.RemoverEspacos(transportadora.InscricaoEstadual),
                EnderecoCompleto = FormatarTexto.RemoverEspacos(logradouro),
                NomeMunicipio = FormatarTexto.RemoverEspacos(transportadora.Cidade),
                SiglaUF = FormatarTexto.RemoverEspacos(transportadora.SiglaUf)
            };

            if (ValidacaoCpfCnpj.ValidarCnpj(transportadora.CpfCnpj))
            {
                transportadoraXml.Cnpj = FormatarTexto.ManterSomenteNumeros(transportadora.CpfCnpj);
            }
            else
            {
                transportadoraXml.Cpf = FormatarTexto.ManterSomenteNumeros(transportadora.CpfCnpj);
            }

            return transportadoraXml;
        }
    }
}
