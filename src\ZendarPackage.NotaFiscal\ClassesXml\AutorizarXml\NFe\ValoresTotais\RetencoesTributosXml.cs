﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.ValoresTotais
{
    [XmlRoot(ElementName = "retTrib")]
    public class RetencoesTributosXml
    {
        [XmlElement(ElementName = "vRetPIS")]
        public string ValorRetidoPIS { get; set; }

        [XmlElement(ElementName = "vRetCOFINS")]
        public string ValorRetidoCOFINS { get; set; }

        [XmlElement(ElementName = "vRetCSLL")]
        public string ValorRetidoCSLL { get; set; }

        [XmlElement(ElementName = "vBCIRRF")]
        public string BaseCalculoIRRF { get; set; }

        [XmlElement(ElementName = "vIRRF")]
        public string ValorIRRF { get; set; }

        [XmlElement(ElementName = "vBCRetPrev")]
        public string BaseCalculoRetidoPrevidenciaSocial { get; set; }

        [XmlElement(ElementName = "vRetPrev")]
        public string ValorRetidoPrevidenciaSocial { get; set; }
    }
}
