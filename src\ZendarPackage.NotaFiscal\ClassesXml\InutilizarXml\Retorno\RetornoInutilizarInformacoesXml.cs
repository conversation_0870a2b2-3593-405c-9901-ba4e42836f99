﻿using System;
using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.InutilizarXml.Retorno
{
    [XmlRoot(ElementName = "infInut", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class RetornoInutilizarInformacoesXml
    {
        [XmlAttribute("Id")]
        public string Id { get; set; }

        [XmlElement(ElementName = "tpAmb", Order = 1, IsNullable = false)]
        public int TipoAmbiente { get; set; }

        [XmlElement(ElementName = "verAplic", Order = 2, IsNullable = false)]
        public string VersaoAplicativo { get; set; }

        [XmlElement(ElementName = "cStat", Order = 3, IsNullable = false)]
        public int CodigoStatus { get; set; }

        [XmlElement(ElementName = "xMotivo", Order = 4, IsNullable = false)]
        public string Motivo { get; set; }

        [XmlElement(ElementName = "cUF", Order = 5, IsNullable = false)]
        public int CodigoUF { get; set; }

        [XmlElement(ElementName = "ano", Order = 6, IsNullable = false)]
        public int Ano { get; set; }

        [XmlElement(ElementName = "CNPJ", Order = 7, IsNullable = false)]
        public string Cnpj { get; set; } // CNPJ do emitente

        [XmlElement(ElementName = "mod", Order = 8, IsNullable = false)]
        public int Modelo { get; set; }

        [XmlElement(ElementName = "serie", Order = 9, IsNullable = false)]
        public string Serie { get; set; }

        [XmlElement(ElementName = "nNFIni", Order = 10, IsNullable = false)]
        public string NumeroNFeInicial { get; set; }

        [XmlElement(ElementName = "nNFFin", Order = 11, IsNullable = false)]
        public string NumeroNFeFinal { get; set; }

        [XmlElement(ElementName = "dhRecbto", Order = 12, IsNullable = false)]
        public DateTime DataHoraRecebimento { get; set; }

        [XmlElement(ElementName = "nProt", Order = 13, IsNullable = false)]
        public string NumeroProtocolo { get; set; }
    }
}
