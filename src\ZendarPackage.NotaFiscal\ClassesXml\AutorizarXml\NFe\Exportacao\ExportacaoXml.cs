﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Exportacao
{
    [XmlRoot(ElementName = "exporta")]
    public class ExportacaoXml
    {
        [XmlElement(ElementName = "UFSaidaPais")]
        public string SiglaUFSaidaPais { get; set; }

        [XmlElement(ElementName = "xLocExporta")]
        public string LocalEmbarque { get; set; }

        [XmlElement(ElementName = "xLocDespacho")]
        public string LocalDespacho { get; set; }

        public static ExportacaoXml ConverterXml(DestinoOperacao destinoOperacao, OperacaoFiscal tipoOperacao, Classes.Autorizar.Exportacao exportacao)
        {
            if (destinoOperacao == DestinoOperacao.OPERACAO_EXTERIOR && tipoOperacao == OperacaoFiscal.SAIDA && exportacao != null)
            {
                return new ExportacaoXml
                {
                    SiglaUFSaidaPais = FormatarTexto.RemoverEspacos(exportacao.UfEmbarque),
                    LocalEmbarque = FormatarTexto.RemoverEspacos(exportacao.LocalEmbarque)
                };
            }

            return null;
        }
    }
}
