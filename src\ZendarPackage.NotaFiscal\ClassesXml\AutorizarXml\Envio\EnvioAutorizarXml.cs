﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe;
using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.Envio
{
    [XmlRoot(ElementName = "enviNFe", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class EnvioAutorizarXml
    {
        [XmlAttribute("versao")]
        public string Versao { get; set; }

        [XmlElement(ElementName = "idLote")]
        public string NumeroLote { get; set; }

        [XmlElement(ElementName = "indSinc")]
        public int IndicadorSincrono { get; set; }

        [XmlElement(ElementName = "NFe")]
        public NFeXml[] NFeXml { get; set; }

        public static EnvioAutorizarXml ConverterXml(string versao, ModeloFiscal modeloFiscal, NFeXml nfeXml)
        {
            return new EnvioAutorizarXml
            {
                Versao = versao,
                NumeroLote = "000000000000001",
                IndicadorSincrono = modeloFiscal == ModeloFiscal.NFCe
                                    ? (int)TipoEnvioNotaFiscal.SINCRONO
                                    : (int)TipoEnvioNotaFiscal.ASSINCRONO,
                NFeXml = new NFeXml[] { nfeXml }
            };
        }
    }
}
