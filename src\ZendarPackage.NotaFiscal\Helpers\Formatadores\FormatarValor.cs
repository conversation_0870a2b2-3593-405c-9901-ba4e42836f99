﻿using System.Globalization;

namespace ZendarPackage.NotaFiscal.Helpers.Formatadores
{
    public static class FormatarValor
    {
        public static string FormatarValorXmlNotaFiscal(decimal valor, int casasDecimais = 2)
        {
            var formatoCasasDecimais = "".PadRight(casasDecimais, '0');

            return valor.ToString($"0.{formatoCasasDecimais}", CultureInfo.InvariantCulture);
        }
    }
}
