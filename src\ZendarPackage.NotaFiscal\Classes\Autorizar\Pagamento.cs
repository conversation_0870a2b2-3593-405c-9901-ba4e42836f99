﻿using System;
using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.Classes.Autorizar
{
	public class Pagamento
    {
        public IndicadorPagamento IndicadorPagamento { get; set; }
        public MeioPagamentoFiscal MeioPagamento { get; set; }
        public decimal Valor { get; set; }
        public decimal Troco { get; set; }
        public string CnpjCredenciadora { get; set; }
        public IntegracaoPagamento? IntegracaoPagamento { get; set; }
        public BandeiraCartao? BandeiraCartao { get; set; }
        public string NumeroAutorizacao { get; set; }
        public string CnpjBeneficiario { get; set; }
        public string TerminalPagamentoId { get; set; }
        public string Parcela { get; set; }
        public DateTime DataVencimento { get; set; }
        public DateTime? DataPagamento { get; set; }
        public InformacaoPagamento? InformacoesPagamento { get; set; }
    }
}
