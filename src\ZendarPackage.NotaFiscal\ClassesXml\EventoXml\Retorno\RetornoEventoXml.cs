﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.EventoXml.Retorno
{
    [XmlRoot(ElementName = "retEvento", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class RetornoEventoXml
    {
        [XmlAttribute("versao")]
        public string Versao { get; set; }

        [XmlElement(ElementName = "infEvento", Order = 1, IsNullable = false)]
        public RetornoEventoInformacoesXml InformacoesEventoXml { get; set; }

        [XmlElement(ElementName = "Signature", Order = 2, IsNullable = false)]
        public string Assinatura { get; set; }
    }
}
