﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Classes.Autorizar;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Cofins;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Icms;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Ipi;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Pis;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto
{
    [XmlRoot(ElementName = "imposto")]
    public class ImpostosXml
    {
        [XmlElement(ElementName = "vTotTrib")]
        public string ValorTotalTributos { get; set; }

        [XmlElement(ElementName = "vItem12741")]
        public string ValorTributos12741 { get; set; }

        [XmlElement(ElementName = "ICMS")]
        public IcmsXml Icms { get; set; }

        [XmlElement(ElementName = "IPI")]
        public IpiXml Ipi { get; set; }

        [XmlElement(ElementName = "II")]
        public ImpostoImportacaoXml ImpostoImportacao { get; set; }

        [XmlElement(ElementName = "PIS")]
        public PisXml Pis { get; set; }

        [XmlElement(ElementName = "COFINS")]
        public CofinsXml Cofins { get; set; }

        [XmlElement(ElementName = "ICMSUFDest")]
        public IcmsUfDestinoXml IcmsUfDestino { get; set; }

        public static ImpostosXml ConverterXml(Classes.Autorizar.Item item, InformacoesAutorizacao informacoesAutorizacao)
        {
            var tributosProdutoXml = new ImpostosXml
            {
                ValorTotalTributos = FormatarValor.FormatarValorXmlNotaFiscal(item.ValorTotalTributos),
                Icms = IcmsXml.ConverterXml(item, informacoesAutorizacao.ModeloFiscal, informacoesAutorizacao.RegrasAtivas),
                Ipi = IpiXml.ConverterXml(item.IpiCst, item.IpiBaseCalculo, item.IpiAliquota, item.IpiValor, item.EnquadramentoLegal),
                ImpostoImportacao = ImpostoImportacaoXml.ConverterXml(item.ValorTotal, item.ImpostoImportacao),
                Pis = PisXml.ConverterXml(item.PisCst, item.PisBaseCalculo, item.PisAliquota, item.PisValor),
                Cofins = CofinsXml.ConverterXml(item.CofinsCst, item.CofinsBaseCalculo, item.CofinsAliquota, item.CofinsValor)
            };

            //Operação Interestadual (tag: idDest = 2) com Consumidor Final (tag: indFinal = 1) e Não Contribuinte (tag: indIEDest = 9) e (tag: CRT != 1)
            if (informacoesAutorizacao.IdentificadorDestinoOperacao == DestinoOperacao.OPERACAO_INTERESTADUAL &&
                informacoesAutorizacao.IndicaOperacaoConsumidorFinal == OperacaoConsumidorFinal.CONSUMIDOR_FINAL &&
                informacoesAutorizacao.Destinatario.IndicadorIe == IndicadorIe.NAO_CONTRIBUINTE &&
                informacoesAutorizacao.Finalidade != FinalidadeNotaFiscal.DEVOLUCAO &&
                informacoesAutorizacao.Emitente.RegimeTributario != RegimeTributario.SIMPLES_NACIONAL)
            {
                tributosProdutoXml.IcmsUfDestino = IcmsUfDestinoXml.ConverterXml(item);
            }


            return tributosProdutoXml;
        }
    }
}
