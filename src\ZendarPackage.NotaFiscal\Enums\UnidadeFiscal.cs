﻿using System.ComponentModel;

namespace ZendarPackage.NotaFiscal.Enums
{
    public enum UnidadeFiscal
    {
        AMPOLA = 1,
        BALDE = 2,
        BANDEJ = 3,
        BARRA = 4,
        BISNAG = 5,
        BLOCO = 6,
        BOBINA = 7,
        BOMB = 8,
        CAPS = 9,
        CART = 10,
        CENTO = 11,
        CJ = 12,
        CM = 13,
        CM2 = 14,
        CX = 15,
        CX2 = 16,
        CX3 = 17,
        CX5 = 18,
        CX10 = 19,
        CX15 = 20,
        CX20 = 21,
        CX25 = 22,
        CX50 = 23,
        CX100 = 24,
        DISP = 25,
        DUZIA = 26,
        EMBAL = 27,
        FARDO = 28,
        FOLHA = 29,
        FRASCO = 30,
        GALAO = 31,
        GF = 32,
        GRAMAS = 33,
        JOGO = 34,
        KG = 35,
        KIT = 36,
        LATA = 37,
        LITRO = 38,
        M = 39,
        M2 = 40,
        M3 = 41,
        MILHEI = 42,
        ML = 43,
        MWH = 44,
        PACOTE = 45,
        PALETE = 46,
        PARES = 47,
        PC = 48,
        POTE = 49,
        K = 50,
        RESMA = 51,
        ROLO = 52,
        SACO = 53,
        SACOLA = 54,
        TAMBOR = 55,
        TANQUE = 56,
        TON = 57,
        TU<PERSON> = 58,
        UNID = 59,
        VASIL = 60,
        VIDRO = 61,
        UN = 62
    }
}
