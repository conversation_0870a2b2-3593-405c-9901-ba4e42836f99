﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Produto.Combustivel;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Produto.Exportacao;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Produto.Importacao;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Produto
{
    [XmlRoot(ElementName = "prod")]
    public class ProdutoXml
    {
        [XmlElement(ElementName = "cProd")]
        public string Codigo { get; set; }

        [XmlElement(ElementName = "cEAN")]
        public string CodigoEAN { get; set; }

        [XmlElement(ElementName = "xProd")]
        public string Descricao { get; set; }

        [XmlElement(ElementName = "NCM")]
        public string Ncm { get; set; }

        [XmlElement(ElementName = "CEST")]
        public string Cest { get; set; }

        [XmlElement(ElementName = "indEscala")]
        public string IndEscala { get; set; }

        [XmlElement(ElementName = "CNPJFab")]
        public string CNPJFab { get; set; }

        [XmlElement(ElementName = "cBenef")]
        public string BeneficioFiscal { get; set; }

        [XmlElement(ElementName = "EXTIPI")]
        public string Ex_tipi { get; set; }

        [XmlElement(ElementName = "CFOP")]
        public string Cfop { get; set; }

        [XmlElement(ElementName = "uCom")]
        public string Unidade { get; set; }

        [XmlElement(ElementName = "qCom")]
        public string Quantidade { get; set; }

        [XmlElement(ElementName = "vUnCom")]
        public string ValorUnitario { get; set; }

        [XmlElement(ElementName = "vProd")]
        public string ValorTotalBruto { get; set; }

        [XmlElement(ElementName = "cEANTrib")]
        public string CodigoEANTributado { get; set; }

        [XmlElement(ElementName = "uTrib")]
        public string UnidadeTributavel { get; set; }

        [XmlElement(ElementName = "qTrib")]
        public string QuantidadeTributavel { get; set; }

        [XmlElement(ElementName = "vUnTrib")]
        public string ValorUnitarioTributacao { get; set; }

        [XmlElement(ElementName = "vFrete")]
        public string ValorFrete { get; set; }

        [XmlElement(ElementName = "vSeg")]
        public string ValorSeguro { get; set; }

        [XmlElement(ElementName = "vDesc")]
        public string ValorDesconto { get; set; }

        [XmlElement(ElementName = "vOutro")]
        public string ValorOutrasDespesas { get; set; }

        [XmlElement(ElementName = "indTot")]
        public int IndicadorValorNoTotalNota { get; set; }

        [XmlElement(ElementName = "DI")]
        public DeclaracaoImportacaoXml[] DeclaracaoImportacao { get; set; }

        [XmlElement(ElementName = "detExport")]
        public GrupoInformacaoExportacaoXml[] GrupoInformacaoExportacao { get; set; }

        [XmlElement(ElementName = "xPed")]
        public string NumeroPedidoCompra { get; set; }

        [XmlElement(ElementName = "nItemPed")]
        public int NumeroItemPedido { get; set; }

        [XmlElement(ElementName = "nFCI")]
        public string NumeroFCI { get; set; }

        [XmlElement(ElementName = "rastro")]
        public RastroXml Rastro { get; set; }

        [XmlElement(ElementName = "veicProd")]
        public VeiculosNovosXml VeiculosNovos { get; set; }

        [XmlElement(ElementName = "med")]
        public MedicamentoXml Medicamento { get; set; }

        [XmlElement(ElementName = "arma")]
        public ArmaXml Arma { get; set; }

        [XmlElement(ElementName = "comb")]
        public CombustivelXml Combustivel { get; set; }

        public static ProdutoXml ConverterXml(Classes.Autorizar.Item item, AmbienteFiscal ambienteFiscal, ModeloFiscal modelo, string numeroPedidoExterno)
        {
            var produtoXml = new ProdutoXml
            {
                Codigo = FormatarTexto.RemoverEspacos(item.CodigoProduto),
                CodigoEAN = FormatarTexto.RemoverEspacos(item.CodigoBarras),
                Descricao = ambienteFiscal == AmbienteFiscal.HOMOLOGACAO && modelo == ModeloFiscal.NFCe
                                   ? "NOTA FISCAL EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL"
                                   : FormatarTexto.RemoverEspacos(item.DescricaoProduto),
                Ncm = FormatarTexto.RemoverEspacos(item.Ncm),
                Cest = FormatarTexto.RemoverEspacos(item.CodigoCest),
                IndEscala = FormatarTexto.RemoverEspacos(item.IndicadorEscalaRelevante),
                CNPJFab = FormatarTexto.RemoverEspacos(item.CnpjFabricante),
                BeneficioFiscal = FormatarTexto.RemoverEspacos(item.CodigoBeneficioFiscal),
                Cfop = FormatarTexto.RemoverEspacos(item.Cfop),
                Unidade = FormatarTexto.RemoverEspacos(item.UnidadeMedida),
                Quantidade = FormatarValor.FormatarValorXmlNotaFiscal(item.Quantidade, 4),
                ValorUnitario = FormatarValor.FormatarValorXmlNotaFiscal(item.ValorUnitario, 6),
                ValorTotalBruto = FormatarValor.FormatarValorXmlNotaFiscal(item.ValorTotal),
                CodigoEANTributado = FormatarTexto.RemoverEspacos(item.CodigoBarras),
                UnidadeTributavel = FormatarTexto.RemoverEspacos(item.UnidadeTributavel),
                QuantidadeTributavel = FormatarValor.FormatarValorXmlNotaFiscal(item.QuantidadeTributavel, 4),
                ValorUnitarioTributacao = FormatarValor.FormatarValorXmlNotaFiscal(item.ValorUnitarioTributavel, 6),
                ValorFrete = item.ValorFrete > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.ValorFrete) : null,
                ValorSeguro = item.ValorSeguro > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.ValorSeguro) : null,
                ValorDesconto = item.ValorDesconto > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.ValorDesconto) : null,
                ValorOutrasDespesas = item.ValorOutrasDespesas > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.ValorOutrasDespesas) : null,
                IndicadorValorNoTotalNota = (int)item.IndicadorValorTotalNota,
                DeclaracaoImportacao = DeclaracaoImportacaoXml.ConverterXml(item.ImpostoImportacao, item.Adicoes),
                GrupoInformacaoExportacao = GrupoInformacaoExportacaoXml.ConverterXml(item.Exportacoes),
                Combustivel = CombustivelXml.ConverterXml(item)
            };

            numeroPedidoExterno = FormatarTexto.ManterSomenteNumerosELetras(numeroPedidoExterno);
            if (!string.IsNullOrEmpty(numeroPedidoExterno))
            {
                produtoXml.NumeroPedidoCompra = numeroPedidoExterno;
                produtoXml.NumeroItemPedido = item.NumeroItem;
            }
            else if (!string.IsNullOrEmpty(item.NumeroPedidoCompra) && item.ItemPedidoCompra.HasValue)
            {
                produtoXml.NumeroPedidoCompra = FormatarTexto.RemoverEspacos(item.NumeroPedidoCompra);
                produtoXml.NumeroItemPedido = item.ItemPedidoCompra.Value;
            }

            return produtoXml;
        }
    }
}
