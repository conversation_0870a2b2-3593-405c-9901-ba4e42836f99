﻿using System.Collections.Generic;
using System.Linq;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Icms
{
    [XmlRoot(ElementName = "ICMS61")]
    public class Icms61Xml
    {
        [XmlElement(ElementName = "orig")]
        public int Origem { get; set; }

        [XmlElement(ElementName = "CST")]
        public int Cst { get; set; }

        [XmlElement(ElementName = "qBCMonoRet")]
        public decimal QuantidadeBcMonoRetido { get; set; }

        [XmlElement(ElementName = "adRemICMSRet")]
        public decimal AliquotaadremIcmsRetido { get; set; }

        [XmlElement(ElementName = "vICMSMonoRet")]
        public decimal ValorIcmsMonoRetido { get; set; }


        public static Icms61Xml ConverterXml(Classes.Autorizar.Item item)
        {
            return new Icms61Xml
            {
                Origem = (int)item.CstOrigem,
                Cst = (int)item.CstCsosn,
                QuantidadeBcMonoRetido = item.QuantidadeBCMonoRetido,
                AliquotaadremIcmsRetido = item.AliquotaAdREmICMSRetido,
                ValorIcmsMonoRetido = item.ValorIcmsMonoRetido
            };
        }
    }
}
