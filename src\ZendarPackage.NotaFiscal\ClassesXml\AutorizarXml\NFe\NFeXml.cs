﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Classes.Autorizar;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.InformacoesSuplementares;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe
{
    [XmlRoot(ElementName = "NFe", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class NFeXml
    {
        [XmlElement(ElementName = "infNFe")]
        public InformacoesNFeXml InformacoesNFeXml { get; set; }

        [XmlElement(ElementName = "infNFeSupl")]
        public InformacoesSuplementaresXml InformacoesSuplementaresXml { get; set; }

        [XmlElement(ElementName = "Signature", Namespace = "http://www.w3.org/2000/09/xmldsig#")]
        public AssinaturaXml AssinaturaXml { get; set; }

        public static NFeXml ConverterXml(InformacoesAutorizacao informacoesAutorizacao)
        {
            return new NFeXml
            {
                InformacoesNFeXml = InformacoesNFeXml.ConverterXml(informacoesAutorizacao),
                InformacoesSuplementaresXml = InformacoesSuplementaresXml.ConverterXml(informacoesAutorizacao.ModeloFiscal, informacoesAutorizacao.InformacoesSuplementares)
            };
        }
    }
}
