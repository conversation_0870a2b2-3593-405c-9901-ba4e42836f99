﻿using System.Collections.Generic;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Classes.Autorizar;
using ZendarPackage.NotaFiscal.Helpers;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Produto.Exportacao
{
    [XmlRoot(ElementName = "detExport")]
    public class GrupoInformacaoExportacaoXml
    {
        [XmlElement(ElementName = "nDraw")]
        public string NumeroDrawback { get; set; }

        [XmlElement(ElementName = "exportInd")]
        public GrupoExportacaoIndiretaXml GrupoExportacaoIndireta { get; set; }

        public static GrupoInformacaoExportacaoXml[] ConverterXml(List<ItemExportacao> exportacoes)
        {
            var listaInformacoesExportacaoXml = new List<GrupoInformacaoExportacaoXml>();

            foreach (var itemExportacao in exportacoes)
            {
                listaInformacoesExportacaoXml.Add(new GrupoInformacaoExportacaoXml
                {
                    NumeroDrawback = FormatarTexto.RemoverEspacos(itemExportacao.NumeroDrawBack),
                    GrupoExportacaoIndireta = new GrupoExportacaoIndiretaXml
                    {
                        NumeroRegistroExportacao = FormatarTexto.RemoverEspacos(itemExportacao.NumeroRegistroExportacao),
                        ChaveAcesso = FormatarTexto.RemoverEspacos(itemExportacao.ChaveNfeRecebida),
                        QtdeItemRealmenteExportada = FormatarValor.FormatarValorXmlNotaFiscal(itemExportacao.QuantidadeExportada, 4)
                    }
                });
            }

            return listaInformacoesExportacaoXml.Count > 0 ? listaInformacoesExportacaoXml.ToArray() : null;
        }
    }
}
