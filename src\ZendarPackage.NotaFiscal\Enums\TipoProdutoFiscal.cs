﻿using System.ComponentModel;

namespace ZendarPackage.NotaFiscal.Enums
{
    public enum TipoProdutoFiscal
    {
        [Description("Mercadoria para a revenda")]
        Mercadoria_para_revenda = 0,
        [Description("Matéria prima")]
        Materia_Prima = 1,
        [Description("Embalagem")]
        Embalagem = 2,
        [Description("Produto em processo")]
        Produto_em_Processo = 3,
        [Description("Produto acabado")]
        Produto_Acabado = 4,
        [Description("Subproduto")]
        Subproduto = 5,
        [Description("Produto intermediário")]
        Produto_Intermediario = 6,
        [Description("Material de uso e consumo")]
        Material_de_Uso_e_Consumo = 7,
        [Description("Ativo imobilizado")]
        Ativo_Imobilizado = 8,
        [Description("Serviços")]
        Servicos = 9,
        [Description("Outros insumos")]
        Outros_insumos = 10,
        [Description("Outros")]
        Outras = 99
    }
}
