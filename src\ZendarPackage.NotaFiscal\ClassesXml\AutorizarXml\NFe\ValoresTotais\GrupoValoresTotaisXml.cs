﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Classes.Autorizar;
using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.ValoresTotais
{
    [XmlRoot(ElementName = "total")]
    public class GrupoValoresTotaisXml
    {
        [XmlElement(ElementName = "ICMSTot")]
        public ValoresTotaisIcmsXml ValoresTotaisIcms { get; set; }

        [XmlElement(ElementName = "ISSQNtot")]
        public ValoresTotaisIssqnXml ValoresTotaisIssqnXml { get; set; }

        [XmlElement(ElementName = "retTrib")]
        public RetencoesTributosXml RetencoesTributosXml { get; set; }

        public static GrupoValoresTotaisXml ConverterXml(Totais totais, FinalidadeNotaFiscal finalidade, bool possuiItens)
        {
            return new GrupoValoresTotaisXml
            {
                ValoresTotaisIcms = ValoresTotaisIcmsXml.ConverterXml(totais, finalidade, possuiItens)
            };
        }
    }
}
