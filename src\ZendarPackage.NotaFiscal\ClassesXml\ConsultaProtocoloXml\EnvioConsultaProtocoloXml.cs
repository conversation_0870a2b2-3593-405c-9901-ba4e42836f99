﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.ConsultaProtocoloXml
{
    [XmlRoot(ElementName = "consSitNFe", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class EnvioConsultaProtocoloXml
    {
        [XmlAttribute("versao")]
        public string Versao { get; set; }

        [XmlElement(ElementName = "tpAmb", Order = 1, IsNullable = false)]
        public int TipoAmbiente { get; set; }

        [XmlElement(ElementName = "xServ", Order = 2, IsNullable = false)]
        public string ServicoSolicitado = "CONSULTAR";

        [XmlElement(ElementName = "chNFe", Order = 3, IsNullable = false)]
        public string ChaveAcesso { get; set; }
    }
}
