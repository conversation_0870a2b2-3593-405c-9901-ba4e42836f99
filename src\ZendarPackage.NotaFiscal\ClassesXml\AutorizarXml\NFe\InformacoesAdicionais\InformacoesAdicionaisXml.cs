﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.InformacoesAdicionais
{
    [XmlRoot(ElementName = "infAdic")]
    public class InformacoesAdicionaisXml
    {
        [XmlElement(ElementName = "infAdFisco")]
        public string InformacoesFisco { get; set; }

        [XmlElement(ElementName = "infCpl")]
        public string InformacoesComplementares { get; set; }

        [XmlElement(ElementName = "obsCont")]
        public CampoLivreContribuinteXml CampoLivreContribuinteXml { get; set; }

        [XmlElement(ElementName = "obsFisco")]
        public CampoLivreFiscoXml[] CampoLivreFiscoXml { get; set; }

        [XmlElement(ElementName = "procRef")]
        public ProcessoReferenciadoXml[] ProcessoReferenciadoXml { get; set; }

        public static InformacoesAdicionaisXml ConverterXml(Classes.Autorizar.InformacoesAdicionais informacoesAdicionais)
        {
            if (string.IsNullOrEmpty(informacoesAdicionais.DadosAdicionais))
            {
                return null;
            }

            return new InformacoesAdicionaisXml
            {
                InformacoesComplementares = FormatarTexto.RemoverEspacos($"{informacoesAdicionais.BaseLegal} {informacoesAdicionais.DadosAdicionais}")
            };
        }
    }
}
