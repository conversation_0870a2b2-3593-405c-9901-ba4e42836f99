﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml;
using System.Xml.Schema;
using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.Helpers.ManipuladorXml
{
    public static class ConverterXml
    {
        public static XmlDocument ConverterObjetoParaXmlDocument(object objeto, Dictionary<string, string> namespaceDeclaration)
        {
            try
            {
                if (objeto != null)
                {
                    var settings = new XmlWriterSettings
                    {
                        Encoding = new UTF8Encoding(false, false),
                        Indent = false,
                        OmitXmlDeclaration = true
                    };

                    var ns = new XmlSerializerNamespaces();
                    foreach (var pair in namespaceDeclaration)
                    {
                        ns.Add(pair.Key, pair.Value);
                    }

                    var serializer = new XmlSerializer(objeto.GetType());
                    using var textWriter = new StringWriter();
                    using (var xmlWriter = XmlWriter.Create(textWriter, settings))
                    {
                        serializer.Serialize(xmlWriter, objeto, ns);
                    }

                    var xmlDocument = new XmlDocument
                    {
                        PreserveWhitespace = false
                    };
                    xmlDocument.LoadXml(textWriter.ToString());

                    XmlDeclaration xmldecl;
                    xmldecl = xmlDocument.CreateXmlDeclaration("1.0", "UTF-8", null);

                    //Add the new node to the document.
                    XmlElement root = xmlDocument.DocumentElement;
                    xmlDocument.InsertBefore(xmldecl, root);

                    return xmlDocument;
                }

                return null;
            }
            catch (Exception e)
            {
                throw new Exception("Falha ao converter um objeto em XmlDocument.", e);
            }
        }

        public static object ConverterStringXmlParaObjeto(string xml, Type type)
        {
            try
            {
                var doc = new XmlDocument
                {
                    PreserveWhitespace = false
                };
                doc.LoadXml(xml.Trim());

                var reader = new XmlTextReader(new StringReader(doc.OuterXml));
                var serializer = new XmlSerializer(type);
                return serializer.Deserialize(reader);
            }
            catch (Exception e)
            {
                throw new Exception("Falha ao converter uma string em objeto.", e);
            }
        }

        public static bool TryConverterStringXmlParaObjeto<T>(string xml, out T outObj) where T : class
        {
            try
            {
                var doc = new XmlDocument
                {
                    PreserveWhitespace = false
                };
                doc.LoadXml(xml.Trim());

                var reader = new XmlTextReader(new StringReader(doc.OuterXml));
                var type = Activator.CreateInstance<T>();
                var serializer = new XmlSerializer(type.GetType());
                outObj = (T)serializer.Deserialize(reader);
                return true;
            }
            catch
            {
                outObj = null;
                return false;
            }
        }

        public static string MsgValidacao { get; set; } = "";

        public static bool ValidarSchemaXml(string xml, Dictionary<string, string> schemas)
        {
            MsgValidacao = "";
            var readerSettings = new XmlReaderSettings();
            foreach (var schema in schemas)
            {
                readerSettings.Schemas.Add(schema.Value, schema.Key);
            }
            readerSettings.ValidationType = ValidationType.Schema;
            readerSettings.ValidationEventHandler += new ValidationEventHandler(Reader_ValidationEventHandler);

            XmlReader books = XmlReader.Create(new XmlTextReader(new StringReader(xml)), readerSettings);

            while (books.Read()) { }

            return string.IsNullOrEmpty(MsgValidacao);
        }

        private static void Reader_ValidationEventHandler(object sender, ValidationEventArgs e)
        {
            MsgValidacao += $" Erro: {e.Exception.Message}";
        }
    }
}
