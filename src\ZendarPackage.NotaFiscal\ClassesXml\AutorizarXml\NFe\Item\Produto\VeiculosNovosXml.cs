﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Produto
{
    [XmlRoot(ElementName = "veicProd")]
    public class VeiculosNovosXml
    {
        [XmlElement(ElementName = "tpOp")]
        public int TipoOperacao { get; set; }

        [XmlElement(ElementName = "chassi")]
        public string Chassi { get; set; }

        [XmlElement(ElementName = "cCor")]
        public string CodigoCorMontadora { get; set; }

        [XmlElement(ElementName = "xCor")]
        public string DescricaoCor { get; set; }

        [XmlElement(ElementName = "pot")]
        public string Potencia { get; set; }

        [XmlElement(ElementName = "cilin")]
        public string <PERSON>ilindrada { get; set; }

        [XmlElement(ElementName = "pesoL")]
        public string PesoLiquido { get; set; }

        [XmlElement(ElementName = "pesoB")]
        public string PesoBruto { get; set; }

        [XmlElement(ElementName = "nSerie")]
        public string NumeroSerie { get; set; }

        [XmlElement(ElementName = "tpComb")]
        public string TipoCombustivel { get; set; }

        [XmlElement(ElementName = "nMotor")]
        public string NumeroMotor { get; set; }

        [XmlElement(ElementName = "CMT")]
        public string CapacidadeMaxima { get; set; }

        [XmlElement(ElementName = "dist")]
        public string DistanciaEntreEixos { get; set; }

        [XmlElement(ElementName = "anoMod")]
        public string AnoModeloFabricacao { get; set; }

        [XmlElement(ElementName = "anoFab")]
        public string AnoFabricacao { get; set; }

        [XmlElement(ElementName = "tpPint")]
        public string TipoPintura { get; set; }

        [XmlElement(ElementName = "tpVeic")]
        public string TipoVeiculo { get; set; }

        [XmlElement(ElementName = "espVeic")]
        public int EspecieVeiculo { get; set; }

        [XmlElement(ElementName = "VIN")]
        public string CondicaoVIN { get; set; }

        [XmlElement(ElementName = "condVeic")]
        public int CondicaoVeiculo { get; set; }

        [XmlElement(ElementName = "cMod")]
        public string CodigoMarcaModelo { get; set; }

        [XmlElement(ElementName = "cCorDENATRAN")]
        public string CodigoCorDenatran { get; set; }

        [XmlElement(ElementName = "lota")]
        public int CapacidadeMaximaLotacao { get; set; }

        [XmlElement(ElementName = "tpRest")]
        public int Restricao { get; set; }
    }
}
