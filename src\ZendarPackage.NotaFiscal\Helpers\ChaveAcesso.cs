﻿using System;
using System.Collections.Generic;
using System.Linq;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.Helpers
{
    public static class ChaveAcesso
    {
        public static string Gerar(InformacoesCompoeChaveAcesso informacoesChaveAcesso)
        {
            var chaveAcesso = $"{informacoesChaveAcesso.EmitenteCodEstado}" +
                              $"{informacoesChaveAcesso.DataEmissao:yyMM}" +
                              $"{FormatarTexto.ManterSomenteNumeros(informacoesChaveAcesso.EmitenteCpfCnpj).PadLeft(14, '0')}" +
                              $"{informacoesChaveAcesso.ModeloFiscal}" +
                              $"{informacoesChaveAcesso.NumeroSerie.ToString().PadLeft(3, '0')}" +
                              $"{informacoesChaveAcesso.Numero}" +
                              $"{informacoesChaveAcesso.TipoEmissao}" +
                              $"{informacoesChaveAcesso.CodigoNf}";

            char[] array = chaveAcesso.ToCharArray();
            Array.Reverse(array);
            var chaveAcessoInvertida = new string(array);

            var tuple = new List<Tuple<int, int, int>>();
            int contador = 2;

            for (int i = 0; i < chaveAcessoInvertida.Length; i++)
            {
                if (contador == 10)
                {
                    contador = 2;
                }

                var item = Convert.ToInt32(chaveAcessoInvertida[i].ToString());

                tuple.Add(Tuple.Create(item, contador, item * contador));

                contador++;
            }

            var somatoria = tuple.Sum(t => t.Item3);
            var restoDivisao = somatoria % 11;

            int digitoVerificador = 0;
            if (restoDivisao > 1)
            {
                digitoVerificador = 11 - restoDivisao;
            }

            chaveAcesso += digitoVerificador;

            if (!chaveAcesso.Length.Equals(44))
            {
                return null;
            }

            return chaveAcesso;
        }
    }

    public class InformacoesCompoeChaveAcesso
    {
        public int EmitenteCodEstado { get; set; }
        public string EmitenteCpfCnpj { get; set; }
        public DateTime DataEmissao { get; set; }
        public int ModeloFiscal { get; set; }
        public int NumeroSerie { get; set; }
        public string Numero { get; set; }
        public int TipoEmissao { get; set; }
        public int CodigoNf { get; set; }
    }
}
