﻿namespace ZendarPackage.NotaFiscal.Enums
{
    public enum IcmsCstCsosn
    {
        //Cst
        CST_TRIBUTADA_INTEGRALMENTE = 0,
        CST_TRIBUTADA_COM_COBRANCA_POR_SUBSTITUICAO_TRIBUTARIA = 10,
        CST_REDUCAO_BASE_CALCULO = 20,
        CST_ISENTA_NAO_TRIBUTADA_COM_COBRANCA_POR_SUBSTITUICAO_TRIBUTARIA = 30,
        CST_ISENTA = 40,
        CST_NAO_TRIBUTADA = 41,
        CST_SUSPENSAO = 50,
        CST_DIFERIMENTO = 51,
        CST_ICMS_COBRADO_ANTERIORMENTE_POR_SUBSTITUICAO_TRIBUTARIA = 60,
        CST_REDUCAO_BASE_CALCULO_COBRANCA_POR_SUBSTITUICAO_TRIBUTARIA = 70,
        CST_OUTROS = 90,
        CST_MONOFASICA_COMBUSTIVEIS = 61,

        //Csosn
        CSOSN_TRIBUTADA_SIMPLES_NACIONAL_COM_PERMISSAO_CREDITO = 101,
        CSOSN_TRIBUTADA_SIMPLES_NACIONAL_SEM_PERMISSAO_CREDITO = 102,
        CSOSN_ISENCAO_ICMS_SIMPLES_NACIONAL_FAIXA_RECEITA_BRUTA = 103,
        CSOSN_IMUNE = 300,
        CSOSN_NAO_TRIBUTADA_SIMPLES_NACIONAL = 400,
        CSOSN_TRIBUTADA_SIMPLES_NACIONAL_COM_PERMISSAO_CREDITO_COM_COBRANCA_POR_SUBSTITUICAO_TRIBUTARIA = 201,
        CSOSN_TRIBUTADA_SIMPLES_NACIONAL_SEM_PERMISSAO_CREDITO_COM_COBRANCA_POR_SUBSTITUICAO_TRIBUTARIA = 202,
        CSOSN_ISENCAO_ICMS_SIMPLES_NACIONAL_FAIXA_RECEITA_BRUTA_COM_COBRANCA_POR_SUBSTITUICAO_TRIBUTARIA = 203,
        CSOSN_ICMS_COBRADO_ANTERIORMENTE = 500,
        CSOSN_OUTROS = 900
    }
}
