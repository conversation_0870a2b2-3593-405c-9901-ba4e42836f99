﻿using System;
using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.StatusServicoXml
{
    [XmlRoot(ElementName = "retConsStatServ", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class RetornoStatusServicoXml
    {
        [XmlAttribute("versao")]
        public string Versao { get; set; }

        [XmlElement(ElementName = "tpAmb", Order = 1, IsNullable = false)]
        public int TipoAmbiente { get; set; }

        [XmlElement(ElementName = "verAplic", Order = 2, IsNullable = false)]
        public string VersaoAplicativo { get; set; }

        [XmlElement(ElementName = "cStat", Order = 3, IsNullable = false)]
        public int CodigoStatus { get; set; }

        [XmlElement(ElementName = "xMotivo", Order = 4, IsNullable = false)]
        public string Motivo { get; set; }

        [XmlElement(ElementName = "cUF", Order = 5, IsNullable = false)]
        public int CodigoUF { get; set; }

        [XmlElement(ElementName = "dhRecbto", Order = 6, IsNullable = false)]
        public DateTime DataHoraRecebimento { get; set; }

        [XmlElement(ElementName = "tMed", Order = 7, IsNullable = false)]
        public int TempoMedioResposta { get; set; }

        [XmlElement(ElementName = "dhRetorno", Order = 8, IsNullable = false)]
        public DateTime DataHoraPrevistoRetorno { get; set; }

        [XmlElement(ElementName = "xObs", Order = 9, IsNullable = false)]
        public string InformacoesAdicionais { get; set; }
    }
}
