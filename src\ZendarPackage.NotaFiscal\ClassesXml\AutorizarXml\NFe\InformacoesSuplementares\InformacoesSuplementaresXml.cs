﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.InformacoesSuplementares
{
    [XmlRoot(ElementName = "infNFeSupl")]
    public class InformacoesSuplementaresXml
    {
        [XmlElement(ElementName = "qrCode")]
        public string QrCode { get; set; }

        [XmlElement(ElementName = "urlChave")]
        public string UrlChave { get; set; }

        public static InformacoesSuplementaresXml ConverterXml(ModeloFiscal modeloFiscal, Classes.Autorizar.InformacoesSuplementares informacoesSuplementares)
        {
            if (modeloFiscal == ModeloFiscal.NFCe)
            {
                return new InformacoesSuplementaresXml
                {
                    QrCode = FormatarTexto.RemoverEspacos(informacoesSuplementares.QrCode),
                    UrlChave = FormatarTexto.RemoverEspacos(informacoesSuplementares.UrlConsultaChaveAcesso)
                };
            }

            return null;
        }
    }
}
