﻿using System.Security.Cryptography.X509Certificates;
using System.Security.Cryptography.Xml;
using System.Xml;

namespace ZendarPackage.NotaFiscal.Helpers.ManipuladorXml
{
    public static class AssinarXml
    {
        public static (XmlDocument xml, string mensagem) Assinar(object objeto, string uri, X509Certificate2 certificado)
        {
            var xmlDocument = ConverterXml.ConverterObjetoParaXmlDocument(objeto, PacoteLiberacaoSchemas.NamespaceDeclaration);
            if (xmlDocument == null)
            {
                return (null, "Não foi possível converter o objeto em xml.");
            }

            var doc = new XmlDocument
            {
                PreserveWhitespace = false
            };
            doc.LoadXml(xmlDocument.InnerXml);

            var reference = new Reference()
            {
                DigestMethod = "http://www.w3.org/2000/09/xmldsig#sha1"
            };

            if (!string.IsNullOrEmpty(uri))
            {
                int qtdeRefUri = doc.GetElementsByTagName(uri).Count;
                if (qtdeRefUri == 0 || qtdeRefUri > 1)
                {
                    return (null, "Não foi possível assinar o xml.");
                }

                var _uri = doc.GetElementsByTagName(uri).Item(0).Attributes;
                foreach (XmlAttribute _atributo in _uri)
                {
                    if (_atributo.Name == "Id")
                    {
                        reference.Uri = $"#{_atributo.InnerText}";
                    }
                }
            }
            else
            {
                reference.Uri = "";
            }

            reference.AddTransform(new XmlDsigEnvelopedSignatureTransform());
            reference.AddTransform(new XmlDsigC14NTransform());

            var keyInfo = new KeyInfo();
            keyInfo.AddClause(new KeyInfoX509Data(certificado));

            var signedXml = new SignedXml(doc)
            {
                KeyInfo = keyInfo,
                SigningKey = certificado.PrivateKey,
                SignedInfo =
                {
                    SignatureMethod = "http://www.w3.org/2000/09/xmldsig#rsa-sha1"
                }
            };

            signedXml.AddReference(reference);
            signedXml.ComputeSignature();

            doc.DocumentElement.AppendChild(doc.ImportNode(signedXml.GetXml(), true));

            return (doc, "");
        }
    }
}
