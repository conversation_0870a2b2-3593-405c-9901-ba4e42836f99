﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.ValoresTotais
{
    [XmlRoot(ElementName = "ISSQNtot")]
    public class ValoresTotaisIssqnXml
    {
        [XmlElement(ElementName = "vServ")]
        public string ValorServico { get; set; }

        [XmlElement(ElementName = "vBC")]
        public string ValorBaseCalculo { get; set; }

        [XmlElement(ElementName = "vISS")]
        public string ValorISS { get; set; }

        [XmlElement(ElementName = "vPIS")]
        public string ValorPIS { get; set; }

        [XmlElement(ElementName = "vCOFINS")]
        public string ValorCOFINS { get; set; }

        [XmlElement(ElementName = "dCompet")]
        public string DataPrestacaoServico { get; set; }

        [XmlElement(ElementName = "vDeducao")]
        public string ValorTotalDeducao { get; set; }

        [XmlElement(ElementName = "vOutro")]
        public string ValorOutrasRetencoes { get; set; }

        [XmlElement(ElementName = "vDescIncond")]
        public string ValorDescontoIncondicionado { get; set; }

        [XmlElement(ElementName = "vDescCond")]
        public string ValorDescontoCondicionado { get; set; }

        [XmlElement(ElementName = "vISSRet")]
        public string ValorISSRetido { get; set; }

        [XmlElement(ElementName = "cRegTrib")]
        public string CodigoRegimeEspecialDeTributacao { get; set; }
    }
}
