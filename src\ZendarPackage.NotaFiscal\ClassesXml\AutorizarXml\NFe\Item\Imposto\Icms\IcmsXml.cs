﻿using System;
using System.Collections.Generic;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Icms
{
    [XmlRoot(ElementName = "ICMS")]
    public class IcmsXml
    {
        [XmlElement(ElementName = "ICMS00")]
        public Icms00Xml Icms00 { get; set; }

        [XmlElement(ElementName = "ICMS10")]
        public Icms10Xml Icms10 { get; set; }

        [XmlElement(ElementName = "ICMS20")]
        public Icms20Xml Icms20 { get; set; }

        [XmlElement(ElementName = "ICMS30")]
        public Icms30Xml Icms30 { get; set; }

        [XmlElement(ElementName = "ICMS40")]
        public Icms40Xml Icms40 { get; set; }

        [XmlElement(ElementName = "ICMS51")]
        public Icms51Xml Icms51 { get; set; }

        [XmlElement(ElementName = "ICMS60")]
        public Icms60Xml Icms60 { get; set; }

        [XmlElement(ElementName = "ICMS61")]
        public Icms61Xml Icms61 { get; set; }

        [XmlElement(ElementName = "ICMS70")]
        public Icms70Xml Icms70 { get; set; }

        [XmlElement(ElementName = "ICMS90")]
        public Icms90Xml Icms90 { get; set; }

        [XmlElement(ElementName = "ICMSPart")]
        public IcmsPartXml IcmsPart { get; set; }

        [XmlElement(ElementName = "ICMSST")]
        public IcmsStXml IcmsSt { get; set; }

        [XmlElement(ElementName = "ICMSSN101")]
        public IcmsSn101Xml IcmsSn101 { get; set; }

        [XmlElement(ElementName = "ICMSSN102")]
        public IcmsSn102Xml IcmsSn102 { get; set; }

        [XmlElement(ElementName = "ICMSSN201")]
        public IcmsSn201Xml IcmsSn201 { get; set; }

        [XmlElement(ElementName = "ICMSSN202")]
        public IcmsSn202Xml IcmsSn202 { get; set; }

        [XmlElement(ElementName = "ICMSSN500")]
        public IcmsSn500Xml IcmsSn500 { get; set; }

        [XmlElement(ElementName = "ICMSSN900")]
        public IcmsSn900Xml IcmsSn900 { get; set; }

        public static IcmsXml ConverterXml(Classes.Autorizar.Item item, ModeloFiscal modelo, List<string> regrasAtivas)
        {
            var grupoIcmsXml = new IcmsXml();

            switch (item.CstCsosn)
            {
                //Tributada integralmente
                case IcmsCstCsosn.CST_TRIBUTADA_INTEGRALMENTE:
                    grupoIcmsXml.Icms00 = Icms00Xml.ConverterXml(item);
                    break;

                //Tributada e com cobrança do ICMS por substituição tributária
                case IcmsCstCsosn.CST_TRIBUTADA_COM_COBRANCA_POR_SUBSTITUICAO_TRIBUTARIA:
                    grupoIcmsXml.Icms10 = Icms10Xml.ConverterXml(item);
                    break;

                //Com redução de base de cálculo
                case IcmsCstCsosn.CST_REDUCAO_BASE_CALCULO:
                    grupoIcmsXml.Icms20 = Icms20Xml.ConverterXml(item);
                    break;

                //Isenta ou não tributada e com cobrança do ICMS por substituição tributária
                case IcmsCstCsosn.CST_ISENTA_NAO_TRIBUTADA_COM_COBRANCA_POR_SUBSTITUICAO_TRIBUTARIA:
                    grupoIcmsXml.Icms30 = Icms30Xml.ConverterXml(item);
                    break;

                case IcmsCstCsosn.CST_ISENTA:
                case IcmsCstCsosn.CST_NAO_TRIBUTADA:
                case IcmsCstCsosn.CST_SUSPENSAO:
                    grupoIcmsXml.Icms40 = Icms40Xml.ConverterXml(item);
                    break;

                case IcmsCstCsosn.CST_DIFERIMENTO:
                    grupoIcmsXml.Icms51 = Icms51Xml.ConverterXml(item);
                    break;

                //ICMS cobrado anteriormente por substituição tributária
                case IcmsCstCsosn.CST_ICMS_COBRADO_ANTERIORMENTE_POR_SUBSTITUICAO_TRIBUTARIA:
                    grupoIcmsXml.Icms60 = Icms60Xml.ConverterXml(item, modelo, regrasAtivas);
                    break;

                //Tributação monofásica sobre combustíveis cobrada anteriormente;
                case IcmsCstCsosn.CST_MONOFASICA_COMBUSTIVEIS:
                    grupoIcmsXml.Icms61 = Icms61Xml.ConverterXml(item);
                    break;

                //Com redução de base de cálculo e cobrança do ICMS por substituição tributária
                case IcmsCstCsosn.CST_REDUCAO_BASE_CALCULO_COBRANCA_POR_SUBSTITUICAO_TRIBUTARIA:
                    grupoIcmsXml.Icms70 = Icms70Xml.ConverterXml(item);
                    break;

                case IcmsCstCsosn.CST_OUTROS:
                    grupoIcmsXml.Icms90 = Icms90Xml.ConverterXml(item);
                    break;

                //Tributada pelo Simples Nacional com permissão de crédito.
                case IcmsCstCsosn.CSOSN_TRIBUTADA_SIMPLES_NACIONAL_COM_PERMISSAO_CREDITO:
                    grupoIcmsXml.IcmsSn101 = IcmsSn101Xml.ConverterXml(item);
                    break;


                case IcmsCstCsosn.CSOSN_TRIBUTADA_SIMPLES_NACIONAL_SEM_PERMISSAO_CREDITO:
                case IcmsCstCsosn.CSOSN_ISENCAO_ICMS_SIMPLES_NACIONAL_FAIXA_RECEITA_BRUTA:
                case IcmsCstCsosn.CSOSN_IMUNE:
                case IcmsCstCsosn.CSOSN_NAO_TRIBUTADA_SIMPLES_NACIONAL:
                    grupoIcmsXml.IcmsSn102 = IcmsSn102Xml.ConverterXml(item.CstOrigem, item.CstCsosn);
                    break;

                //Tributada pelo Simples Nacional com permissão de crédito e com cobrança do ICMS por Substituição Tributária
                case IcmsCstCsosn.CSOSN_TRIBUTADA_SIMPLES_NACIONAL_COM_PERMISSAO_CREDITO_COM_COBRANCA_POR_SUBSTITUICAO_TRIBUTARIA:
                    grupoIcmsXml.IcmsSn201 = IcmsSn201Xml.ConverterXml(item);
                    break;

                //202=Tributada pelo Simples Nacional sem permissão de crédito e com cobrança do ICMS por Substituição Tributária
                //203 - Isenção do ICMS nos Simples Nacional para faixa de receita bruta e com cobrança do ICMS por Substituição Tributária
                case IcmsCstCsosn.CSOSN_TRIBUTADA_SIMPLES_NACIONAL_SEM_PERMISSAO_CREDITO_COM_COBRANCA_POR_SUBSTITUICAO_TRIBUTARIA:
                case IcmsCstCsosn.CSOSN_ISENCAO_ICMS_SIMPLES_NACIONAL_FAIXA_RECEITA_BRUTA_COM_COBRANCA_POR_SUBSTITUICAO_TRIBUTARIA:
                    grupoIcmsXml.IcmsSn202 = IcmsSn202Xml.ConverterXml(item);
                    break;

                //ICMS cobrado anteriormente por substituição tributária(substituído) ou por antecipação
                case IcmsCstCsosn.CSOSN_ICMS_COBRADO_ANTERIORMENTE:
                    grupoIcmsXml.IcmsSn500 = IcmsSn500Xml.ConverterXml(item, modelo, regrasAtivas);
                    break;

                case IcmsCstCsosn.CSOSN_OUTROS:
                    grupoIcmsXml.IcmsSn900 = IcmsSn900Xml.ConverterXml(item);
                    break;
            }


            if (!string.IsNullOrEmpty(item.CodigoAnp) &&
                modelo == ModeloFiscal.NFe &&
                (item.CstCsosn == IcmsCstCsosn.CST_NAO_TRIBUTADA || item.CstCsosn == IcmsCstCsosn.CST_ICMS_COBRADO_ANTERIORMENTE_POR_SUBSTITUICAO_TRIBUTARIA))
            {
                var codigoAnp = new List<int>
                {
                    210203001, 320101001, 320101002, 320102002, 320102001, 320102003, 320102005, 320201001,
                    320103001, 220102001, 320301001, 320103002, 820101032, 820101026, 820101027, 820101004,
                    820101005, 820101022, 820101031, 820101030, 820101014, 820101006, 820101016, 820101015,
                    820101025, 820101017, 820101018, 820101019, 820101020, 820101021, 420105001, 420101005,
                    420101004, 420102005, 420102004, 420104001, 820101033, 820101034, 420106001, 820101011,
                    820101003, 820101013, 820101012, 420106002, 830101001, 420301004, 420202001, 420301001,
                    420301002, 410103001, 410101001, 410102001, 430101004, 510101001, 510101002, 510102001,
                    510102002, 510201001, 510201003, 510301003, 510103001, 510301001
                };

                if (codigoAnp.Contains(Convert.ToInt32(item.CodigoAnp)))
                {
                    grupoIcmsXml.IcmsSt = IcmsStXml.ConverterXml(item, modelo);
                }
            }

            return grupoIcmsXml;
        }
    }
}
