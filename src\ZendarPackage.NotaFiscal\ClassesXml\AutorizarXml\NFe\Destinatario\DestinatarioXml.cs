﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;
using ZendarPackage.NotaFiscal.Helpers.Validacoes;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Destinatario
{
    [XmlRoot(ElementName = "dest")]
    public class DestinatarioXml
    {
        [XmlElement(ElementName = "CNPJ")]
        public string Cnpj { get; set; }

        [XmlElement(ElementName = "CPF")]
        public string Cpf { get; set; }

        [XmlElement(ElementName = "idEstrangeiro")]
        public string IdentificacaoEstrangeiro { get; set; }

        [XmlElement(ElementName = "xNome")]
        public string Nome { get; set; }

        [XmlElement(ElementName = "enderDest")]
        public EnderecoDestinatarioXml EnderecoDestinatario { get; set; }

        [XmlElement(ElementName = "indIEDest")]
        public int IndicadorIE { get; set; }

        [XmlElement(ElementName = "IE")]
        public string Ie { get; set; }

        [XmlElement(ElementName = "ISUF")]
        public string InscricaoSUFRAMA { get; set; }

        [XmlElement(ElementName = "IM")]
        public string InscricaoMunicipal { get; set; }

        [XmlElement(ElementName = "email")]
        public string Email { get; set; }


        public static DestinatarioXml ConverterXml(Classes.Autorizar.Destinatario destinatario, AmbienteFiscal ambienteFiscal)
        {
            if (string.IsNullOrEmpty(destinatario.CpfCnpj) &&
                string.IsNullOrEmpty(destinatario.DocumentoEstrangeiro))
            {
                return null;
            }

            var destinatarioXml = new DestinatarioXml
            {
                Nome = ambienteFiscal == AmbienteFiscal.PRODUCAO
                        ? FormatarTexto.RemoverEspacos(destinatario.Nome)
                        : "NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL",

                IdentificacaoEstrangeiro = FormatarTexto.RemoverEspacos(destinatario.DocumentoEstrangeiro),
                IndicadorIE = (int)destinatario.IndicadorIe,
                Ie = FormatarTexto.ManterSomenteNumeros(destinatario.InscricaoEstadual),
                InscricaoSUFRAMA = !string.IsNullOrEmpty(destinatario.Suframa) ? FormatarTexto.RemoverEspacos(destinatario.Suframa) : null,
                Email = FormatarTexto.RemoverEspacos(destinatario.Email)
            };

            if (ValidacaoCpfCnpj.ValidarCnpj(destinatario.CpfCnpj))
            {
                destinatarioXml.Cnpj = FormatarTexto.ManterSomenteNumeros(destinatario.CpfCnpj);
            }
            else
            {
                destinatarioXml.Cpf = FormatarTexto.ManterSomenteNumeros(destinatario.CpfCnpj);
            }

            if (!string.IsNullOrEmpty(destinatario.Logradouro) &&
                !string.IsNullOrEmpty(destinatario.Numero) &&
                !string.IsNullOrEmpty(destinatario.Bairro))
            {
                destinatarioXml.EnderecoDestinatario = new EnderecoDestinatarioXml
                {
                    Logradouro = FormatarTexto.ManterSomenteNumerosELetras(destinatario.Logradouro),
                    Numero = FormatarTexto.ManterSomenteNumerosELetras(destinatario.Numero),
                    Complemento = FormatarTexto.ManterSomenteNumerosELetras(destinatario.Complemento),
                    Bairro = FormatarTexto.ManterSomenteNumerosELetras(destinatario.Bairro),
                    CodigoMunicipio = FormatarTexto.ManterSomenteNumeros(destinatario.CodigoIbge),
                    NomeMunicipio = FormatarTexto.ManterSomenteNumerosELetras(destinatario.Cidade),
                    SiglaUF = FormatarTexto.ManterSomenteNumerosELetras(destinatario.SiglaUf),
                    Cep = FormatarTexto.ManterSomenteNumeros(destinatario.Cep),
                    CodigoPais = FormatarTexto.ManterSomenteNumeros(destinatario.CodigoPais),
                    NomePais = FormatarTexto.ManterSomenteNumerosELetras(destinatario.Pais),
                    Telefone = FormatarTexto.ManterSomenteNumeros(destinatario.Telefone)
                };
            }

            return destinatarioXml;
        }
    }
}
