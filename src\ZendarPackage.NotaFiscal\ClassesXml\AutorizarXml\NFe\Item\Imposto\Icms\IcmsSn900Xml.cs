﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Icms
{
    [XmlRoot(ElementName = "ICMSSN900")]
    public class IcmsSn900Xml
    {
        [XmlElement(ElementName = "orig")]
        public int Origem { get; set; }

        [XmlElement(ElementName = "CSOSN")]
        public int Csosn { get; set; }

        [XmlElement(ElementName = "modBC")]
        public int ModalidadeBaseCalculoIcms { get; set; }

        [XmlElement(ElementName = "vBC")]
        public string ValorBaseCalculo { get; set; }

        [XmlElement(ElementName = "pRedBC")]
        public string ReducaoBaseCalculoIcms { get; set; }

        [XmlElement(ElementName = "pICMS")]
        public string Aliquota { get; set; }

        [XmlElement(ElementName = "vICMS")]
        public string Valor { get; set; }

        [XmlElement(ElementName = "modBCST")]
        public int ModalidadeBaseCalculoIcmsST { get; set; }

        [XmlElement(ElementName = "pMVAST")]
        public string MargemValorAdicionadoIcmsSt { get; set; }

        [XmlElement(ElementName = "pRedBCST")]
        public string ReducaoBaseCalculoIcmsSt { get; set; }

        [XmlElement(ElementName = "vBCST")]
        public string ValorBaseCalculoIcmsSt { get; set; }

        [XmlElement(ElementName = "pICMSST")]
        public string AliquotaIcmsSt { get; set; }

        [XmlElement(ElementName = "vICMSST")]
        public string ValorIcmsSt { get; set; }

        [XmlElement(ElementName = "vBCFCPST")]
        public string ValorBaseCalculoFCPST { get; set; }

        [XmlElement(ElementName = "pFCPST")]
        public string PercentualFCPST { get; set; }

        [XmlElement(ElementName = "vFCPST")]
        public string ValorFCPST { get; set; }

        [XmlElement(ElementName = "pCredSN")]
        public string AliquotaCredito { get; set; }

        [XmlElement(ElementName = "vCredICMSSN")]
        public string ValorCredito { get; set; }

        public static IcmsSn900Xml ConverterXml(Classes.Autorizar.Item item)
        {
            var icmsXml = new IcmsSn900Xml
            {
                Origem = (int)item.CstOrigem,
                Csosn = (int)item.CstCsosn,
                ModalidadeBaseCalculoIcms = (int)item.ModalidadeIcms,
                ValorBaseCalculo = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsBaseCalculo),
                ReducaoBaseCalculoIcms = item.IcmsReducaoBaseCalculo > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsReducaoBaseCalculo) : null,
                Aliquota = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsAliquota),
                Valor = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsValor),
                ModalidadeBaseCalculoIcmsST = (int)item.ModalidadeIcmsSt,
                MargemValorAdicionadoIcmsSt = item.IcmsStMva > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStMva) : null,
                ReducaoBaseCalculoIcmsSt = item.IcmsStReducaoBaseCalculo > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStReducaoBaseCalculo) : null,
                ValorBaseCalculoIcmsSt = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStBaseCalculo),
                AliquotaIcmsSt = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStAliquota),
                ValorIcmsSt = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStValor),
                AliquotaCredito = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsAproveitamentoAliquota),
                ValorCredito = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsAproveitamentoValor)
            };

            if (item.FcpStAliquota > 0)
            {
                icmsXml.ValorBaseCalculoFCPST = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStBaseCalculo);
                icmsXml.PercentualFCPST = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStAliquota);
                icmsXml.ValorFCPST = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStValor);
            }

            return icmsXml;
        }
    }
}
