﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Icms
{
    [XmlRoot(ElementName = "ICMSSN102")]
    public class IcmsSn102Xml
    {
        [XmlElement(ElementName = "orig")]
        public int Origem { get; set; }

        [XmlElement(ElementName = "CSOSN")]
        public int Csosn { get; set; }

        public static IcmsSn102Xml ConverterXml(OrigemMercadoria cstOrigem, IcmsCstCsosn cstCsosn)
        {
            return new IcmsSn102Xml
            {
                Origem = (int)cstOrigem,
                Csosn = (int)cstCsosn
            };
        }
    }
}
