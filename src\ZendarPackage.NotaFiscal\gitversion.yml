﻿mode: ContinuousDelivery
next-version: 1.0
assembly-versioning-scheme: MajorMinorPatch
assembly-file-versioning-scheme: MajorMinorPatch
major-version-bump-message: '\+semver:\s?(breaking|major)'
minor-version-bump-message: '\+semver:\s?(feature|minor)'
patch-version-bump-message: '\+semver:\s?(bug|patch)'
branches:
 main:
  regex: ^master$|^main$
  mode: ContinuousDelivery
  tag: ''
  increment: Patch
  prevent-increment-of-merged-branch-version: true
  track-merge-target: false
  source-branches: [ 'master' ]
  tracks-release-branches: false
  is-release-branch: false
  is-mainline: true
