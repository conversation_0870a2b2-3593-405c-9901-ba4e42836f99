﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.ClassesXml.ProtocoloXml;

namespace ZendarPackage.NotaFiscal.ClassesXml.ConsultaReciboXml
{
    [XmlRoot(ElementName = "retConsReciNFe", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class RetornoConsultaRecebimentoXml
    {
        [XmlAttribute("versao")]
        public string Versao { get; set; }

        [XmlElement(ElementName = "tpAmb", Order = 1, IsNullable = false)]
        public string TipoAmbiente { get; set; }

        [XmlElement(ElementName = "verAplic", Order = 2, IsNullable = false)]
        public string VersaoAplicativo { get; set; }

        [XmlElement(ElementName = "nRec", Order = 3, IsNullable = false)]
        public string NumeroRecibo { get; set; }

        [XmlElement(ElementName = "cStat", Order = 4, IsNullable = false)]
        public int CodigoStatus { get; set; }

        [XmlElement(ElementName = "xMotivo", Order = 5, IsNullable = false)]
        public string Motivo { get; set; }

        [XmlElement(ElementName = "cUF", Order = 6, IsNullable = false)]
        public int CodigoUF { get; set; }

        [XmlElement(ElementName = "dhRecbto", Order = 7, IsNullable = false)]
        public string DataHoraRecebimento { get; set; }

        [XmlElement(ElementName = "cMsg", Order = 8, IsNullable = false)]
        public string CMsg { get; set; }

        [XmlElement(ElementName = "xMsg", Order = 9, IsNullable = false)]
        public string XMsg { get; set; }

        [XmlElement(ElementName = "protNFe", Order = 10, IsNullable = false)]
        public RetornoProtocoloXml[] ProtocoloXml { get; set; }
    }
}
