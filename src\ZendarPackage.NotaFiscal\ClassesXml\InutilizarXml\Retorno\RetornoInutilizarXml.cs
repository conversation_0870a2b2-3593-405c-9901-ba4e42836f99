﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.InutilizarXml.Retorno
{
    [XmlRoot(ElementName = "retInutNFe", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class RetornoInutilizarXml
    {
        [XmlAttribute("versao")]
        public string Versao { get; set; }

        [XmlElement("infInut", Order = 1, IsNullable = false)]
        public RetornoInutilizarInformacoesXml InformacoesInutilizarXml { get; set; }

        [XmlElement(ElementName = "Signature", Order = 2, IsNullable = false)]
        public AssinaturaXml AssinaturaXml { get; set; }
    }
}
