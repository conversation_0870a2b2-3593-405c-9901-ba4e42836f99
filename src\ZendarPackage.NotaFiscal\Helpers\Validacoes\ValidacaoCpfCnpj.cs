﻿using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.Helpers.Validacoes
{
    internal static class ValidacaoCpfCnpj
    {
        public static bool ValidarCnpj(string texto)
        {
            if (!string.IsNullOrEmpty(texto))
            {
                texto = FormatarTexto.ManterSomenteNumeros(texto);

                return texto.Length == 14;
            }

            return false;
        }

        public static bool ValidarCpf(string texto)
        {
            if (!string.IsNullOrEmpty(texto))
            {
                texto = FormatarTexto.ManterSomenteNumeros(texto);

                return texto.Length == 11;
            }

            return false;
        }
    }
}
