﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Transportadora
{
    [XmlRoot(ElementName = "retTransp")]
    public class RetencaoTransporteXml
    {
        [XmlElement(ElementName = "vServ")]
        public double ValorServico { get; set; }

        [XmlElement(ElementName = "vBCRet")]
        public double BaseCalculoRetencaoICMS { get; set; }

        [XmlElement(ElementName = "pICMSRet")]
        public double AliquotaRetencaoICMS { get; set; }

        [XmlElement(ElementName = "vICMSRet")]
        public double ValorRetidoICMS { get; set; }

        [XmlElement(ElementName = "CFOP")]
        public int Cfop { get; set; }

        [XmlElement(ElementName = "cMunFG")]
        public string CodigoMunicipio { get; set; }
    }
}
