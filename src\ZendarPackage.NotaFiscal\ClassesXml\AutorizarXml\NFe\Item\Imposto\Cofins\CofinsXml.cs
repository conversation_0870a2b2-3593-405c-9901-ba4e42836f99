﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Extensions;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Cofins
{
    [XmlRoot(ElementName = "COFINS")]
    public class CofinsXml
    {
        [XmlElement(ElementName = "COFINSAliq")]
        public CofinsTributadoAliquotaXml CofinsTributadoAliquota { get; set; }

        [XmlElement(ElementName = "COFINSQtde")]
        public CofinsTributadoQuantidadeXml CofinsTributadoQuantidade { get; set; }

        [XmlElement(ElementName = "COFINSNT")]
        public CofinsNaoTributadoXml CofinsNaoTributado { get; set; }

        [XmlElement(ElementName = "COFINSOutr")]
        public CofinsOutrasOperacoesXml CofinsOutrasOperacoes { get; set; }

        public static CofinsXml ConverterXml(PisCofinsCst cst, decimal baseCalculo, decimal aliquota, decimal valor)
        {
            var grupoCofinsXml = new CofinsXml();

            switch (cst)
            {
                //01=Operação Tributável (base de cálculo = valor da operação alíquota normal(cumulativo / não cumulativo))                    
                //02=Operação Tributável(base de cálculo = valor da operação(alíquota diferenciada))
                case PisCofinsCst.OPERACAO_TRIBUTAVEL_VL_OPERACAO_ALIQUOTA_NORMAL:
                case PisCofinsCst.OPERACAO_TRIBUTAVEL_VL_OPERACAO_ALIQUOTA_DIFERENCIADA:
                    grupoCofinsXml.CofinsTributadoAliquota = CofinsTributadoAliquotaXml.ConverterXml(cst, baseCalculo, aliquota, valor);
                    break;

                //03=Operação Tributável (base de cálculo = quantidade vendida x alíquota por unidade de produto)
                case PisCofinsCst.OPERACAO_TRIBUTAVEL_QUANTIDADE_VENDIDA_X_ALIQUOTA_POR_UNIDADE:
                    grupoCofinsXml.CofinsTributadoQuantidade = CofinsTributadoQuantidadeXml.ConverterXml(cst, valor);
                    break;

                //04=Operação Tributável (tributação monofásica (alíquota zero))
                //05=Operação Tributável (Substituição Tributária)
                //06=Operação Tributável (alíquota zero)
                //07=Operação Isenta da Contribuição
                //08=Operação Sem Incidência da Contribuição
                //09=Operação com Suspensão da Contribuição
                case PisCofinsCst.OPERACAO_TRIBUTAVEL_TRIBUTACAO_MONOFASICA:
                case PisCofinsCst.OPERACAO_TRIBUTAVEL_SUBSTITUICAO_TRIBUTARIA:
                case PisCofinsCst.OPERACAO_TRIBUTAVEL_ALIQUOTA_ZERO:
                case PisCofinsCst.OPERACAO_ISENTA_CONTRIBUICAO:
                case PisCofinsCst.OPERACAO_SEM_INCIDENCIA_CONTRIBUICAO:
                case PisCofinsCst.OPERACAO_COM_SUSPENSAO_CONTRIBUICAO:
                    grupoCofinsXml.CofinsNaoTributado = new CofinsNaoTributadoXml
                    {
                        Cst = cst.FormatarValorEnumXmlNotaFiscal()
                    };
                    break;

                default: // 49 até 99 
                    grupoCofinsXml.CofinsOutrasOperacoes = CofinsOutrasOperacoesXml.ConverterXml(cst, baseCalculo, aliquota, valor);
                    break;
            }

            return grupoCofinsXml;
        }
    }
}
