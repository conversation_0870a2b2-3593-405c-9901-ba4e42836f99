﻿using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.Classes.Autorizar
{
    public class Transportadora
    {
        public ModalidadeFrete ModalidadeFrete { get; set; }
        public string CpfCnpj { get; set; }
        public string Nome { get; set; }
        public string InscricaoEstadual { get; set; }
        public string <PERSON>gradouro { get; set; }
        public string Numero { get; set; }
        public string Cidade { get; set; }
        public string SiglaUf { get; set; }
        public string Placa { get; set; }
        public string UfVeiculo { get; set; }
        public string Rntc { get; set; }
        public long QuantidadeVolume { get; set; }
        public string EspecieVolume { get; set; }
        public string MarcaVolume { get; set; }
        public string NumeracaoVolume { get; set; }
        public decimal PesoLiquido { get; set; }
        public decimal PesoBruto { get; set; }
    }
}
