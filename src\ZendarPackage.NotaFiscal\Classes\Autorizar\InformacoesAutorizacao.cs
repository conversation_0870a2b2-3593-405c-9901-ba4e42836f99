﻿using System;
using System.Collections.Generic;
using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.Classes.Autorizar
{
    public class InformacoesAutorizacao : Base
    {
        public string ChaveAcesso { get; set; }
        public int TimeZoneOffset { get; set; }
        public int CodigoNf { get; set; }
        public string NaturezaOperacao { get; set; }
        public ModeloFiscal ModeloFiscal { get; set; }
        public int Serie { get; set; }
        public int Numero { get; set; }
        public DateTime DataEmissao { get; set; }
        public OperacaoFiscal TipoOperacao { get; set; }
        public DestinoOperacao IdentificadorDestinoOperacao { get; set; }
        public FormaEmissaoFiscal TipoEmissao { get; set; }
        public FinalidadeNotaFiscal Finalidade { get; set; }
        public OperacaoConsumidorFinal IndicaOperacaoConsumidorFinal { get; set; }
        public OperacaoPresencaComprador IndicadorPresencaComprador { get; set; }
        public OperacaoIntermediador OperacaoComIntermediador { get; set; }
        public DateTime? DataSaidaEntrada { get; set; }
        public string NumeroPedidoExterno { get; set; }

        public Emitente Emitente { get; set; }
        public Destinatario Destinatario { get; set; }
        public Endereco EnderecoRetirada { get; set; }
        public Endereco EnderecoEntrega { get; set; }
        public List<DocumentoReferenciado> DocumentosReferenciados { get; set; }
        public List<string> AutorizadosObterXml { get; set; }
        public Transportadora Transportadora { get; set; }
        public InformacoesIntermediador InformacoesIntermediador { get; set; }
        public InformacoesAdicionais InformacoesAdicionais { get; set; }
        public ResponsavelTecnico ResponsavelTecnico { get; set; }
        public Exportacao Exportacao { get; set; }
        public Totais Totais { get; set; }
        public List<Pagamento> Pagamentos { get; set; }
        public List<Item> Itens { get; set; }
        public List<string> RegrasAtivas { get; set; }
        public InformacoesSuplementares InformacoesSuplementares { get; set; }
    }
}
