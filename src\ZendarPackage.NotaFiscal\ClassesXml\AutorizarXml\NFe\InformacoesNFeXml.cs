﻿using System.Linq;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Classes.Autorizar;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.AutorizacaoParaObter;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Compra;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Destinatario;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Emitente;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.EnderecosRetiradaEntrega;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Exportacao;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Identificacao;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.InformacoesAdicionais;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Intermediador;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Pagamento;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.ResponsavelTecnico;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Transportadora;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.ValoresTotais;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe
{
    public class InformacoesNFeXml
    {
        [XmlAttribute("versao")]
        public string Versao { get; set; }

        [XmlAttribute("Id")]
        public string Id { get; set; }

        [XmlElement(ElementName = "ide")]
        public IdentificacaoNFeXml Ide { get; set; }

        [XmlElement(ElementName = "emit")]
        public EmitenteXml Emitente { get; set; }

        [XmlElement(ElementName = "avulsa")]
        public IdentificacaoFiscoEmitenteXml IdentificacaoFiscoEmitente { get; set; }

        [XmlElement(ElementName = "dest")]
        public DestinatarioXml Destinatario { get; set; }

        [XmlElement(ElementName = "retirada")]
        public RetiradaXml Retirada { get; set; }

        [XmlElement(ElementName = "entrega")]
        public EntregaXml Entrega { get; set; }

        [XmlElement(ElementName = "autXML")]
        public AutorizacaoParaObterXml[] AutorizadosParaObterXml { get; set; }

        [XmlElement(ElementName = "det")]
        public ItemXml[] Itens { get; set; }

        [XmlElement(ElementName = "total")]
        public GrupoValoresTotaisXml GrupoValoresTotais { get; set; }

        [XmlElement(ElementName = "transp")]
        public GrupoInformacoesTransporteXml GrupoInformacoesTransporte { get; set; }

        [XmlElement(ElementName = "cobr")]
        public CobrancaXml Cobranca { get; set; }

        [XmlElement(ElementName = "pag")]
        public PagXml Pag { get; set; }

        [XmlElement(ElementName = "infIntermed")]
        public InformacoesIntermediadorXml InformacoesIntermediador { get; set; }

        [XmlElement(ElementName = "infAdic")]
        public InformacoesAdicionaisXml InformacoesAdicionais { get; set; }

        [XmlElement(ElementName = "exporta")]
        public ExportacaoXml Exportacao { get; set; }

        [XmlElement(ElementName = "compra")]
        public CompraXml Compra { get; set; }

        [XmlElement(ElementName = "Signature", Namespace = "http://www.w3.org/2000/09/xmldsig#")]
        public AssinaturaXml Assinatura { get; set; }

        [XmlElement(ElementName = "infRespTec")]
        public InformacoesResponsavelTecnicoXml InformacoesResponsavelTecnico { get; set; }

        public static InformacoesNFeXml ConverterXml(InformacoesAutorizacao informacoesAutorizacao)
        {
            return new InformacoesNFeXml
            {
                Versao = informacoesAutorizacao.Versao,
                Id = $"NFe{informacoesAutorizacao.ChaveAcesso}",
                Ide = IdentificacaoNFeXml.ConverterXml(informacoesAutorizacao),
                Emitente = EmitenteXml.ConverterXml(informacoesAutorizacao.Emitente),
                Destinatario = DestinatarioXml.ConverterXml(informacoesAutorizacao.Destinatario, informacoesAutorizacao.AmbienteFiscal),
                Retirada = RetiradaXml.ConverterXml(informacoesAutorizacao.EnderecoRetirada),
                Entrega = EntregaXml.ConverterXml(informacoesAutorizacao.EnderecoEntrega),
                AutorizadosParaObterXml = AutorizacaoParaObterXml.ConverterXml(informacoesAutorizacao.Emitente, informacoesAutorizacao.Destinatario, informacoesAutorizacao.AutorizadosObterXml),
                Itens = ItemXml.ConverterXml(informacoesAutorizacao),
                GrupoValoresTotais = GrupoValoresTotaisXml.ConverterXml(informacoesAutorizacao.Totais, informacoesAutorizacao.Finalidade, informacoesAutorizacao.Itens.Any()),
                GrupoInformacoesTransporte = GrupoInformacoesTransporteXml.ConverterXml(informacoesAutorizacao.Transportadora, informacoesAutorizacao.IdentificadorDestinoOperacao),
                Cobranca = CobrancaXml.ConverterXml(informacoesAutorizacao.ModeloFiscal, informacoesAutorizacao.Totais.ValorTotal, informacoesAutorizacao.Pagamentos),
                Pag = PagXml.ConverterXml(informacoesAutorizacao.Pagamentos),
                InformacoesIntermediador = InformacoesIntermediadorXml.ConverterXml(informacoesAutorizacao.InformacoesIntermediador),
                InformacoesAdicionais = InformacoesAdicionaisXml.ConverterXml(informacoesAutorizacao.InformacoesAdicionais),
                Exportacao = ExportacaoXml.ConverterXml(informacoesAutorizacao.IdentificadorDestinoOperacao, informacoesAutorizacao.TipoOperacao, informacoesAutorizacao.Exportacao),
                InformacoesResponsavelTecnico = InformacoesResponsavelTecnicoXml.ConverterXml(informacoesAutorizacao.ResponsavelTecnico),
            };
        }
    }
}
