﻿using System.Collections.Generic;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Produto.Combustivel
{
    [XmlRoot(ElementName = "comb")]
    public class CombustivelXml
    {
        [XmlElement(ElementName = "cProdANP")]
        public string CodigoANP { get; set; }

        [XmlElement(ElementName = "descANP")]
        public string DescricaoANP { get; set; }

        [XmlElement(ElementName = "pGLP")]
        public string PercentualGLP { get; set; }

        [XmlElement(ElementName = "pGNn")]
        public string PercentualGNn { get; set; }

        [XmlElement(ElementName = "pGNi")]
        public string PercentualGNi { get; set; }

        [XmlElement(ElementName = "vPart")]
        public string ValorPart { get; set; }

        [XmlElement(ElementName = "CODIF")]
        public string Codif { get; set; }

        [XmlElement(ElementName = "qTemp")]
        public string QtdeFaturadaTemperaturaAmbiente { get; set; }

        [XmlElement(ElementName = "UFCons")]
        public string UfConsumo { get; set; }

        [XmlElement(ElementName = "CIDE")]
        public GrupoCideXml GrupoCide { get; set; }

        [XmlElement(ElementName = "origComb")]
        public List<OrigemCombustivelXml> OrigemCombustivel { get; set; }

        public static CombustivelXml ConverterXml(Classes.Autorizar.Item item)
        {
            if (string.IsNullOrEmpty(item.CodigoAnp))
            {
                return null;
            }

            return new CombustivelXml
            {
                OrigemCombustivel = OrigemCombustivelXml.ConverterXml(item.ItemOrigemCombustivel),
                CodigoANP = FormatarTexto.RemoverEspacos(item.CodigoAnp),
                DescricaoANP = FormatarTexto.RemoverEspacos(item.DescricaoProdutoAnp),
                PercentualGLP = item.PercentualGlp > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.PercentualGlp, 4) : null,
                PercentualGNn = item.PercentualGasNacional > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.PercentualGasNacional, 4) : null,
                PercentualGNi = item.PercentualGasImportado > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.PercentualGasImportado, 4) : null,
                ValorPart = item.ValorPartidaGlp > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.ValorPartidaGlp) : null,
                Codif = FormatarTexto.RemoverEspacos(item.CodigoCodif),
                QtdeFaturadaTemperaturaAmbiente = item.QuantidadeFaturada > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.QuantidadeFaturada, 4) : null,
                UfConsumo = FormatarTexto.RemoverEspacos(item.UfConsumo),
                GrupoCide = GrupoCideXml.ConverterXml(item.CideBaseCalculo, item.CideAliquota, item.CideValor),
            };
        }
    }
}
