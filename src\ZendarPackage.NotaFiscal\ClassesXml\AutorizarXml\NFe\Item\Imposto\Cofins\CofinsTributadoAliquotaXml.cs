﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Extensions;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Cofins
{
    [XmlRoot(ElementName = "COFINSAliq")]
    public class CofinsTributadoAliquotaXml
    {
        [XmlElement(ElementName = "CST")]
        public string Cst { get; set; }

        [XmlElement(ElementName = "vBC")]
        public string ValorBaseCalculo { get; set; }

        [XmlElement(ElementName = "pCOFINS")]
        public string AliquotaPercentual { get; set; }

        [XmlElement(ElementName = "vCOFINS")]
        public string Valor { get; set; }

        public static CofinsTributadoAliquotaXml ConverterXml(PisCofinsCst cst, decimal baseCalculo, decimal aliquota, decimal valor)
        {
            return new CofinsTributadoAliquotaXml
            {
                Cst = cst.FormatarValorEnumXmlNotaFiscal(),
                ValorBaseCalculo = FormatarValor.FormatarValorXmlNotaFiscal(baseCalculo),
                AliquotaPercentual = FormatarValor.FormatarValorXmlNotaFiscal(aliquota, 4),
                Valor = FormatarValor.FormatarValorXmlNotaFiscal(valor)
            };
        }
    }
}
