﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Intermediador
{
    [XmlRoot(ElementName = "infIntermed")]
    public class InformacoesIntermediadorXml
    {
        [XmlElement(ElementName = "CNPJ")]
        public string IntermedCnpj { get; set; }

        [XmlElement(ElementName = "idCadIntTran")]
        public string IntermedIdentificador { get; set; }

        public static InformacoesIntermediadorXml ConverterXml(Classes.Autorizar.InformacoesIntermediador informacoesIntermediador)
        {
            if (informacoesIntermediador == null || string.IsNullOrEmpty(informacoesIntermediador.Cnpj) || string.IsNullOrEmpty(informacoesIntermediador.Identificador))
            {
                return null;
            }

            return new InformacoesIntermediadorXml
            {
                IntermedCnpj = FormatarTexto.ManterSomenteNumeros(informacoesIntermediador.Cnpj),
                IntermedIdentificador = informacoesIntermediador.Identificador
            };
        }
    }
}