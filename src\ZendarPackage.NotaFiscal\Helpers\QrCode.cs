﻿using System.Security.Cryptography;
using System.Text;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Extensions;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.Helpers
{
    public static class QrCode
    {
        public static string GerarQrCode(string chaveAcesso, AmbienteFiscal tipoAmbienteFiscal, string csc, string cscToken, string urlConsultaQrCode)
        {
            var hashBase = $"{chaveAcesso}|" +
                           $"{FormatarTexto.ManterSomenteNumeros(VersaoQrCode.v2_00.ObterDescricao().Replace("0", ""))}|" +
                           $"{(int)tipoAmbienteFiscal}|";

            //contingencia
            //if (notaFiscal.FormaEmissao.Equals(AtributosConstantes.TipoEmissao_ContingenciaNFCe))
            //{
            //var (sucesso, xml) = ObterXml(nfeXml, informacoesWebserviceSefaz, "infNFe", validarSchema: false);
            //    hashBase += $"{notaFiscal.DataEmissao.Value.ToLocalTime().ToString("dd")}|{notaProcessada.informacoesNFe.grupoValoresTotais.valoresTotaisICMS.valorNFe}|{Criptografia.GerarHexa(digestValue)}|";
            //}

            hashBase += $"{cscToken.Replace("0", "").Trim()}";

            return $"{urlConsultaQrCode}p={hashBase}|{ConverterParaHexaSHA1($"{hashBase}{csc}")}";

        }

        public static string ConverterParaHexaSHA1(string texto)
        {
            using SHA1Managed sha1 = new();
            var hash = sha1.ComputeHash(Encoding.UTF8.GetBytes(texto));
            var sb = new StringBuilder(hash.Length * 2);

            foreach (byte b in hash)
            {
                sb.Append(b.ToString("X2"));
            }

            return sb.ToString();
        }
    }
}
