﻿using System;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.ClassesXml.ProtocoloXml;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.Retorno
{
    [XmlRoot(ElementName = "retEnviNFe", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class RetornoAutorizarXml
    {
        [XmlAttribute("versao")]
        public string Versao { get; set; }

        [XmlElement(ElementName = "tpAmb", Order = 1, IsNullable = false)]
        public int TipoAmbiente { get; set; }

        [XmlElement(ElementName = "verAplic", Order = 2, IsNullable = false)]
        public string VersaoAplicativo { get; set; }

        [XmlElement(ElementName = "cStat", Order = 3, IsNullable = false)]
        public int CodigoStatus { get; set; }

        [XmlElement(ElementName = "xMotivo", Order = 4, IsNullable = false)]
        public string Motivo { get; set; }

        [XmlElement(ElementName = "cUF", Order = 5, IsNullable = false)]
        public int CodigoUF { get; set; }

        [XmlElement(ElementName = "dhRecbto", Order = 6, IsNullable = false)]
        public DateTime DataHoraRecebimento { get; set; }

        [XmlElement(ElementName = "infRec", Order = 7, IsNullable = false)]
        public RetornoReciboXml ReciboXml { get; set; }

        [XmlElement(ElementName = "protNFe", Order = 8, IsNullable = false)]
        public RetornoProtocoloXml ProtocoloXml { get; set; }
    }
}
