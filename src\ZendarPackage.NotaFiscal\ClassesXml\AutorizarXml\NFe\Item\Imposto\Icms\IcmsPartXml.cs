﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Icms
{
    [XmlRoot(ElementName = "ICMSPart")]
    public class IcmsPartXml
    {
        [XmlElement(ElementName = "orig")]
        public string Origem { get; set; }

        [XmlElement(ElementName = "CST")]
        public int Cst { get; set; }

        [XmlElement(ElementName = "modBC")]
        public string ModalidadeBaseCalculoIcms { get; set; }

        [XmlElement(ElementName = "vBC")]
        public string ValorBaseCalculo { get; set; }

        [XmlElement(ElementName = "pRedBC")]
        public string ReducaoBaseCalculoIcms { get; set; }

        [XmlElement(ElementName = "pICMS")]
        public string Aliquota { get; set; }

        [XmlElement(ElementName = "vICMS")]
        public string Valor { get; set; }

        [XmlElement(ElementName = "modBCST")]
        public string ModalidadeBaseCalculoIcmsST { get; set; }

        [XmlElement(ElementName = "pMVAST")]
        public string MargemValorAdicionadoIcmsSt { get; set; }

        [XmlElement(ElementName = "pRedBCST")]
        public string ReducaoBaseCalculoIcmsSt { get; set; }

        [XmlElement(ElementName = "vBCST")]
        public string ValorBaseCalculoIcmsSt { get; set; }

        [XmlElement(ElementName = "pICMSST")]
        public string AliquotaIcmsSt { get; set; }

        [XmlElement(ElementName = "vICMSST")]
        public string ValorIcmsSt { get; set; }

        [XmlElement(ElementName = "pBCOp")]
        public string PercentualBaseCalculoOperacao { get; set; }

        [XmlElement(ElementName = "UFST")]
        public string UfDevidoIcmsSt { get; set; }
    }
}
