﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.ResponsavelTecnico
{
    [XmlRoot(ElementName = "infRespTec")]
    public class InformacoesResponsavelTecnicoXml
    {
        [XmlElement(ElementName = "CNPJ")]
        public string Cnpj { get; set; }

        [XmlElement(ElementName = "xContato")]
        public string NomeContato { get; set; }

        [XmlElement(ElementName = "email")]
        public string Email { get; set; }

        [XmlElement(ElementName = "fone")]
        public string Fone { get; set; }

        [XmlElement(ElementName = "idCSRT")]
        public string IdentificadorCSRT { get; set; }

        [XmlElement(ElementName = "hashCSRT")]
        public string HashCSRT { get; set; }

        public static InformacoesResponsavelTecnicoXml ConverterXml(Classes.Autorizar.ResponsavelTecnico responsavelTecnico)
        {
            return new InformacoesResponsavelTecnicoXml
            {
                Cnpj = FormatarTexto.ManterSomenteNumeros(responsavelTecnico.Cnpj),
                NomeContato = FormatarTexto.RemoverEspacos(responsavelTecnico.Nome),
                Email = FormatarTexto.RemoverEspacos(responsavelTecnico.Email),
                Fone = FormatarTexto.ManterSomenteNumeros(responsavelTecnico.Telefone)
            };
        }
    }
}
