﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Identificacao
{
    [XmlRoot(ElementName = "refNF")]
    public class NotaFiscalReferenciadaXml
    {
        [XmlElement(ElementName = "cUF")]
        public int CodigoUF { get; set; }

        [XmlElement(ElementName = "AAMM")]
        public string AnoMesEmissao { get; set; }

        [XmlElement(ElementName = "CNPJ")]
        public string Cnpj { get; set; }

        [XmlElement(ElementName = "mod")]
        public int Modelo { get; set; }

        [XmlElement(ElementName = "serie")]
        public int Serie { get; set; }

        [XmlElement(ElementName = "nNF")]
        public string Numero { get; set; }
    }
}
