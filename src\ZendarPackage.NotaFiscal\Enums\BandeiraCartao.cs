﻿using System.ComponentModel;

namespace ZendarPackage.NotaFiscal.Enums
{
    public enum BandeiraCartao
    {
        [Description("Visa")]
        VISA = 1,

        [Description("Mastercard")]
        MASTERCARD = 2,

        [Description("American Express")]
        AMERICAN_EXPRESS = 3,

        [Description("Sorocred")]
        SOROCRED = 4,

        [Description("Diners Club")]
        DINERS_CLUB = 5,

        [Description("Elo")]
        ELO = 6,

        [Description("Hipercard")]
        HIPERCARD = 7,

        [Description("Aura")]
        AURA = 8,

        [Description("Cabal")]
        CABAL = 9,

        [Description("<PERSON>elo")]
        ALELO = 10,

        [Description("Banes Card")]
        BANES_CARD = 11,

        [Description("CalCard")]
        CALCARD = 12,

        [Description("Credz")]
        CREDZ = 13,

        [Description("Discover")]
        DISCOVER = 14,

        [Description("GoodCard")]
        GOODCARD = 15,

        [Description("GreenCard")]
        GREENCARD = 16,

        [Description("Hiper")]
        HIPER = 17,

        [Description("JcB")]
        JCB = 18,

        [Description("Mais")]
        MAIS = 19,

        [Description("MaxVan")]
        MAXVAN = 20,

        [Description("Policard")]
        POLICARD = 21,

        [Description("RedeCompras")]
        REDECOMPRAS = 22,

        [Description("Sodexo")]
        SODEXO = 23,

        [Description("ValeCard")]
        VALECARD = 24,

        [Description("Verocheque")]
        VEROCHEQUE = 25,

        [Description("VR")]
        VR = 26,

        [Description("Ticket")]
        TICKET = 27,

        [Description("Outros")]
        OUTROS = 99
    }
}
