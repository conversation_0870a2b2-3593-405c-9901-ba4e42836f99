﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Extensions;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Cofins
{
    [XmlRoot(ElementName = "COFINSQtde")]
    public class CofinsTributadoQuantidadeXml
    {
        [XmlElement(ElementName = "CST")]
        public string Cst { get; set; }

        [XmlElement(ElementName = "qBCProd")]
        public string QuantidadeVendida { get; set; }

        [XmlElement(ElementName = "vAliqProd")]
        public string AliquotaReais { get; set; }

        [XmlElement(ElementName = "vCOFINS")]
        public string Valor { get; set; }

        public static CofinsTributadoQuantidadeXml ConverterXml(PisCofinsCst cst, decimal valor)
        {
            return new CofinsTributadoQuantidadeXml
            {
                Cst = cst.FormatarValorEnumXmlNotaFiscal(),
                QuantidadeVendida = FormatarValor.FormatarValorXmlNotaFiscal(0, 4),
                AliquotaReais = FormatarValor.FormatarValorXmlNotaFiscal(0, 4),
                Valor = FormatarValor.FormatarValorXmlNotaFiscal(valor)
            };
        }
    }
}
