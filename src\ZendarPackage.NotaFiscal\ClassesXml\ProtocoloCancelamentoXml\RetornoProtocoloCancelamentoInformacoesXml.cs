﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.ProtocoloCancelamentoXml
{
    public class RetornoProtocoloCancelamentoInformacoesXml
    {
        [XmlElement(ElementName = "tpAmb", Order = 1, IsNullable = false)]
        public int TipoAmbiente { get; set; }

        [XmlElement(ElementName = "verAplic", Order = 2, IsNullable = false)]
        public string VersaoAplicativo { get; set; }

        [XmlElement(ElementName = "cStat", Order = 3, IsNullable = false)]
        public int CodigoStatus { get; set; }

        [XmlElement(ElementName = "xMotivo", Order = 4, IsNullable = false)]
        public string Resposta { get; set; }

        [XmlElement(ElementName = "cUF", Order = 5, IsNullable = false)]
        public string Uf { get; set; }

        [XmlElement(ElementName = "chNFe", Order = 6, IsNullable = false)]
        public string ChaveAcesso { get; set; }

        [XmlElement(ElementName = "dhRecbto", Order = 7, IsNullable = false)]
        public string DataHoraRecebimento { get; set; }

        [XmlElement(ElementName = "nProt", Order = 8, IsNullable = false)]
        public string NumeroProtocolo { get; set; }
    }
}
