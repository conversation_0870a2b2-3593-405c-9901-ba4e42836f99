﻿using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Validacoes.Classes;

namespace ZendarPackage.NotaFiscal.Helpers.Validacoes
{
    public static class ValidarInformacoes
    {
        public static (bool sucesso, string mensagem) ValidarInformacoesNotaFiscal(InformacoesValidacao informacoes)
        {
            var (sucesso, mensagem) = ValidarInformacoesGerais(informacoes);
            if (!sucesso) return (false, mensagem);

            (sucesso, mensagem) = ValidarInformacoesEmitente(informacoes.Emitente);
            if (!sucesso) return (false, mensagem);

            (sucesso, mensagem) = ValidarInformacoesDestinatario(informacoes.Destinatario, informacoes.ModeloFiscal, informacoes.DestinoOperacao);
            if (!sucesso) return (false, mensagem);

            return (true, "");
        }

        private static (bool sucesso, string mensagem) ValidarInformacoesGerais(InformacoesValidacao informacoes)
        {
            if (informacoes.Status != StatusFiscal.EM_DIGITACAO && informacoes.Status != StatusFiscal.REJEITADA)
            {
                return (false, "Só é possível transmitir notas fiscais quando o status é igual a Em Digitação ou Rejeitada.");
            }

            if (!informacoes.PossuiItens)
            {
                return (false, "É obrigatório informar pelo menos 1 produto.");
            }

            if ((informacoes.Finalidade == FinalidadeNotaFiscal.COMPLEMENTAR || informacoes.Finalidade == FinalidadeNotaFiscal.DEVOLUCAO) &&
                !informacoes.PossuiNotasReferenciadas)
            {
                return (false, "A nota fiscal precisa ter um documento fiscal referenciado.");
            }

            return (true, "");
        }

        private static (bool sucesso, string mensagem) ValidarInformacoesEmitente(InformacoesEmitente emitente)
        {
            if (emitente == null)
            {
                return (false, "Não foi possível consultar as informações da loja.");
            }

            if (string.IsNullOrEmpty(emitente.CpfCnpj))
            {
                return (false, "O CNPJ da loja não foi informado.");
            }

            if (string.IsNullOrEmpty(emitente.RazaoSocial))
            {
                return (false, "A razão social da loja não foi informada.");
            }

            if (string.IsNullOrEmpty(emitente.Logradouro) ||
                string.IsNullOrEmpty(emitente.Numero) ||
                string.IsNullOrEmpty(emitente.Bairro) ||
                string.IsNullOrEmpty(emitente.Cep))
            {
                return (false, "As informações do endereço da loja estão incompletas. Verifique o preenchimento dos campos: endereço, número, bairro e cep.");
            }

            if (string.IsNullOrEmpty(emitente.InscricaoEstadual))
            {
                return (false, "A inscrição estadual da loja não foi informada.");
            }

            return (true, "");
        }

        private static (bool sucesso, string mensagem) ValidarInformacoesDestinatario(InformacoesDestinatario destinatario, ModeloFiscal modeloFiscal, DestinoOperacao destinoOperacao)
        {
            if (modeloFiscal == ModeloFiscal.NFe)
            {
                if (destinatario == null)
                {
                    return (false, "Não foi possível consultar as informações do cliente.");
                }

                if (string.IsNullOrEmpty(destinatario.RazaoSocial))
                {
                    return (false, "A razão social/nome do cliente não foi informada.");
                }

                if (destinoOperacao == DestinoOperacao.OPERACAO_EXTERIOR && string.IsNullOrEmpty(destinatario.DocumentoEstrangeiro))
                {
                    return (false, "O documento estrangeiro do cliente é obrigatório para operações com o exterior.");
                }

                if (destinoOperacao != DestinoOperacao.OPERACAO_EXTERIOR && string.IsNullOrEmpty(destinatario.CpfCnpj))
                {
                    return (false, "O CPF/CNPJ do cliente é obrigatório.");
                }

                if (string.IsNullOrEmpty(destinatario.Logradouro) ||
                    string.IsNullOrEmpty(destinatario.Numero) ||
                    string.IsNullOrEmpty(destinatario.Bairro))
                {
                    return (false, "As informações do endereço do cliente estão incompletas. Verifique o preenchimento dos campos: endereço, número e bairro.");
                }
            }

            return (true, "");
        }
    }
}
