﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Icms
{
    [XmlRoot(ElementName = "ICMS40")]
    public class Icms40Xml
    {
        [XmlElement(ElementName = "orig")]
        public int Origem { get; set; }

        [XmlElement(ElementName = "CST")]
        public int Cst { get; set; }

        [XmlElement(ElementName = "vICMSDeson")]
        public string ValorDesonerado { get; set; }

        [XmlElement(ElementName = "motDesICMS")]
        public string MotivoDesoneracao { get; set; }

        public static Icms40Xml ConverterXml(Classes.Autorizar.Item item)
        {
            var icmsXml = new Icms40Xml
            {
                Origem = (int)item.CstOrigem,
                Cst = (int)item.CstCsosn
            };

            if (item.IcmsMotivoDesoneracao.HasValue)
            {
                icmsXml.ValorDesonerado = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsDesoneradoValor);
                icmsXml.MotivoDesoneracao = item.IcmsMotivoDesoneracao.HasValue ? ((int)item.IcmsMotivoDesoneracao.Value).ToString() : "";
            }

            return icmsXml;
        }
    }
}
