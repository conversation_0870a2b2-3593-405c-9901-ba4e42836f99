﻿using System.Collections.Generic;
using System.Linq;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Classes.Autorizar;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Produto.Combustivel
{
    [XmlRoot(ElementName = "CIDE")]
    public class OrigemCombustivelXml
    {
        [XmlElement(ElementName = "indImport")]
        public string IndicadorImportacao { get; set; }

        [XmlElement(ElementName = "cUFOrig")]
        public string CodigoUFOrigem { get; set; }

        [XmlElement(ElementName = "pOrig")]
        public string PorcentagemOrigem { get; set; }

        public static List<OrigemCombustivelXml> ConverterXml(List<ItemOrigemCombustivel> listaItemOrigemCombustivel)
        {
            return listaItemOrigemCombustivel
                .Select(item => new OrigemCombustivelXml
                {
                    CodigoUFOrigem = item.CodigoUFOrigem.ToString(),
                    IndicadorImportacao = item.IndicadorImportacao,
                    PorcentagemOrigem = FormatarValor.FormatarValorXmlNotaFiscal(item.PorcentagemOrigem)
                })
                .ToList();
        }
    }
}
