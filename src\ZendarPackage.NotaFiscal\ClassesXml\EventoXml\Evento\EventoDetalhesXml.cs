﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.EventoXml.Evento
{
    [XmlRoot(ElementName = "detEvento", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class EventoDetalhesXml
    {
        [XmlAttribute("versao")]
        public string Versao { get; set; }

        [XmlElement(ElementName = "descEvento", Order = 1, IsNullable = false)]
        public string DescricaoEvento { get; set; }

        [XmlElement(ElementName = "nProt", Order = 2, IsNullable = false)]
        public string NumeroProtocolo { get; set; }

        [XmlElement(ElementName = "xJust", Order = 3, IsNullable = false)]
        public string Justificativa { get; set; }

        [XmlElement(ElementName = "xCorrecao", Order = 4, IsNullable = false)]
        public string Correcao { get; set; }

        [XmlElement(ElementName = "xCondUso", Order = 5, IsNullable = false)]
        public string Condicao { get; set; }
    }
}
