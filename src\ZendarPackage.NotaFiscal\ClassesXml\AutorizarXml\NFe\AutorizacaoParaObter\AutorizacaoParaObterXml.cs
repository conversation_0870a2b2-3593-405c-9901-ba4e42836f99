﻿using System.Collections.Generic;
using System.Linq;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;
using ZendarPackage.NotaFiscal.Helpers.Validacoes;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.AutorizacaoParaObter
{
	[XmlRoot(ElementName = "autXML")]
	public class AutorizacaoParaObterXml
	{
		[XmlElement(ElementName = "CNPJ")]
		public string Cnpj { get; set; }

		[XmlElement(ElementName = "CPF")]
		public string Cpf { get; set; }

		public static AutorizacaoParaObterXml[] ConverterXml(
			Classes.Autorizar.Emitente emitente,
			Classes.Autorizar.Destinatario destinatario,
			List<string> listaCpfCnpjAutorizados)
		{
			var listaObterXml = new List<AutorizacaoParaObterXml>();

			if (listaCpfCnpjAutorizados.Count == 0) return listaObterXml.ToArray();

			var cpfCnpjEmitenteDestinatario = new[] { emitente.Cnpj, destinatario.CpfCnpj };

			foreach (var cpfCnpj in listaCpfCnpjAutorizados.Where(cpfCnpj => !cpfCnpjEmitenteDestinatario.Contains(cpfCnpj)))
			{
				var autorizadoXml = new AutorizacaoParaObterXml();

				if (ValidacaoCpfCnpj.ValidarCnpj(cpfCnpj))
				{
					autorizadoXml.Cnpj = FormatarTexto.ManterSomenteNumeros(cpfCnpj);
				}
				else
				{
					autorizadoXml.Cpf = FormatarTexto.ManterSomenteNumeros(cpfCnpj);
				}

				listaObterXml.Add(autorizadoXml);
			}

			return listaObterXml.ToArray();
		}
	}
}
