﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Destinatario
{
    [XmlRoot(ElementName = "enderDest")]
    public class EnderecoDestinatarioXml
    {
        [XmlElement(ElementName = "xLgr")]
        public string Logradouro { get; set; }

        [XmlElement(ElementName = "nro")]
        public string Numero { get; set; }

        [XmlElement(ElementName = "xCpl")]
        public string Complemento { get; set; }

        [XmlElement(ElementName = "xBairro")]
        public string <PERSON>rro { get; set; }

        [XmlElement(ElementName = "cMun")]
        public string CodigoMunicipio { get; set; }

        [XmlElement(ElementName = "xMun")]
        public string NomeMunicipio { get; set; }

        [XmlElement(ElementName = "UF")]
        public string SiglaUF { get; set; }

        [XmlElement(ElementName = "CEP")]
        public string Cep { get; set; }

        [XmlElement(ElementName = "cPais")]
        public string CodigoPais { get; set; }

        [XmlElement(ElementName = "xPais")]
        public string NomePais { get; set; }

        [XmlElement(ElementName = "fone")]
        public string Telefone { get; set; }
    }
}
