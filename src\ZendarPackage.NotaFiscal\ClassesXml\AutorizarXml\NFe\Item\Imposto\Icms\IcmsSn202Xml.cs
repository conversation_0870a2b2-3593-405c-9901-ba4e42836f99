﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Icms
{
    [XmlRoot(ElementName = "ICMSSN202")]
    public class IcmsSn202Xml
    {
        [XmlElement(ElementName = "orig")]
        public int Origem { get; set; }

        [XmlElement(ElementName = "CSOSN")]
        public int Csosn { get; set; }

        [XmlElement(ElementName = "modBCST")]
        public int ModalidadeBaseCalculoIcmsST { get; set; }

        [XmlElement(ElementName = "pMVAST")]
        public string MargemValorAdicionadoIcmsSt { get; set; }

        [XmlElement(ElementName = "pRedBCST")]
        public string ReducaoBaseCalculoIcmsSt { get; set; }

        [XmlElement(ElementName = "vBCST")]
        public string ValorBaseCalculoIcmsSt { get; set; }

        [XmlElement(ElementName = "pICMSST")]
        public string AliquotaIcmsSt { get; set; }

        [XmlElement(ElementName = "vICMSST")]
        public string ValorIcmsSt { get; set; }

        [XmlElement(ElementName = "vBCFCPST")]
        public string ValorBaseCalculoFCPST { get; set; }

        [XmlElement(ElementName = "pFCPST")]
        public string PercentualFCPST { get; set; }

        [XmlElement(ElementName = "vFCPST")]
        public string ValorFCPST { get; set; }

        public static IcmsSn202Xml ConverterXml(Classes.Autorizar.Item item)
        {
            var icmsXml = new IcmsSn202Xml
            {
                Origem = (int)item.CstOrigem,
                Csosn = (int)item.CstCsosn,
                ModalidadeBaseCalculoIcmsST = (int)item.ModalidadeIcmsSt,
                MargemValorAdicionadoIcmsSt = item.IcmsStMva > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStMva) : null,
                ReducaoBaseCalculoIcmsSt = item.IcmsStReducaoBaseCalculo > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStReducaoBaseCalculo) : null,
                ValorBaseCalculoIcmsSt = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStBaseCalculo),
                AliquotaIcmsSt = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStAliquota),
                ValorIcmsSt = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStValor),
            };

            if (item.FcpStAliquota > 0)
            {
                icmsXml.ValorBaseCalculoFCPST = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStBaseCalculo);
                icmsXml.PercentualFCPST = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStAliquota);
                icmsXml.ValorFCPST = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStValor);
            }

            return icmsXml;
        }
    }
}
