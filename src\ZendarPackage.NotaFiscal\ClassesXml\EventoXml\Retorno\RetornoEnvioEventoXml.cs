﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.EventoXml.Retorno
{
    [XmlRoot(ElementName = "retEnvEvento", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class RetornoEnvioEventoXml
    {
        [XmlAttribute("versao")]
        public string Versao { get; set; }

        [XmlElement(ElementName = "idLote", Order = 1, IsNullable = false)]
        public int IdLote { get; set; }

        [XmlElement(ElementName = "tpAmb", Order = 2, IsNullable = false)]
        public int TipoAmbiente { get; set; }

        [XmlElement(ElementName = "verAplic", Order = 3, IsNullable = false)]
        public string VersaoAplicacao { get; set; }

        [XmlElement(ElementName = "cOrgao", Order = 4, IsNullable = false)]
        public int CodigoOrgao { get; set; }

        [XmlElement(ElementName = "cStat", Order = 5, IsNullable = false)]
        public int CodigoStatus { get; set; }

        [XmlElement(ElementName = "xMotivo", Order = 6, IsNullable = false)]
        public string Motivo { get; set; }

        [XmlElement(ElementName = "retEvento", Order = 7, IsNullable = false)]
        public RetornoEventoXml[] RetornoEventoXml { get; set; }
    }
}
