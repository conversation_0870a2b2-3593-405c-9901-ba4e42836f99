﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Extensions;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Pis
{
    [XmlRoot(ElementName = "PIS")]
    public class PisXml
    {
        [XmlElement(ElementName = "PISAliq")]
        public PisTributadoAliquotaXml PisTributadoAliquota { get; set; }

        [XmlElement(ElementName = "PISQtde")]
        public PisTributadoQuantidadeXml PisTributadoQuantidade { get; set; }

        [XmlElement(ElementName = "PISNT")]
        public PisNaoTributadoXml PisNaoTributado { get; set; }

        [XmlElement(ElementName = "PISOutr")]
        public PisOutrasOperacoesXml PisOutrasOperacoes { get; set; }

        public static PisXml ConverterXml(PisCofinsCst cst, decimal baseCalculo, decimal aliquota, decimal valor)
        {
            var grupoPisXml = new PisXml();

            switch (cst)
            {
                //01=Operação Tributável (base de cálculo = valor da operação alíquota normal(cumulativo / não cumulativo))                    
                //02=Operação Tributável(base de cálculo = valor da operação(alíquota diferenciada))
                case PisCofinsCst.OPERACAO_TRIBUTAVEL_VL_OPERACAO_ALIQUOTA_NORMAL:
                case PisCofinsCst.OPERACAO_TRIBUTAVEL_VL_OPERACAO_ALIQUOTA_DIFERENCIADA:
                    grupoPisXml.PisTributadoAliquota = PisTributadoAliquotaXml.ConverterXml(cst, baseCalculo, aliquota, valor);
                    break;

                //03=Operação Tributável (base de cálculo = quantidade vendida x alíquota por unidade de produto)
                case PisCofinsCst.OPERACAO_TRIBUTAVEL_QUANTIDADE_VENDIDA_X_ALIQUOTA_POR_UNIDADE:
                    grupoPisXml.PisTributadoQuantidade = PisTributadoQuantidadeXml.ConverterXml(cst, valor);
                    break;

                //04=Operação Tributável (tributação monofásica (alíquota zero))
                //05=Operação Tributável (Substituição Tributária)
                //06=Operação Tributável (alíquota zero)
                //07=Operação Isenta da Contribuição
                //08=Operação Sem Incidência da Contribuição
                //09=Operação com Suspensão da Contribuição
                case PisCofinsCst.OPERACAO_TRIBUTAVEL_TRIBUTACAO_MONOFASICA:
                case PisCofinsCst.OPERACAO_TRIBUTAVEL_SUBSTITUICAO_TRIBUTARIA:
                case PisCofinsCst.OPERACAO_TRIBUTAVEL_ALIQUOTA_ZERO:
                case PisCofinsCst.OPERACAO_ISENTA_CONTRIBUICAO:
                case PisCofinsCst.OPERACAO_SEM_INCIDENCIA_CONTRIBUICAO:
                case PisCofinsCst.OPERACAO_COM_SUSPENSAO_CONTRIBUICAO:
                    grupoPisXml.PisNaoTributado = new PisNaoTributadoXml
                    {
                        Cst = cst.FormatarValorEnumXmlNotaFiscal()
                    };
                    break;

                // 49 até 99 
                default:
                    grupoPisXml.PisOutrasOperacoes = PisOutrasOperacoesXml.ConverterXml(cst, baseCalculo, aliquota, valor);
                    break;
            }

            return grupoPisXml;
        }
    }
}
