﻿namespace ZendarPackage.NotaFiscal.Classes.Autorizar
{
    public class Totais
    {
        public decimal ValorTotal { get; set; }
        public decimal ValorProdutos { get; set; }
        public decimal IcmsBaseCalculo { get; set; }
        public decimal IcmsValor { get; set; }
        public decimal IcmsDesoneradoValorTotal { get; set; }
        public decimal IcmsFcpValorTotal { get; set; }
        public decimal IcmsStBaseCalculo { get; set; }
        public decimal IcmsStValor { get; set; }
        public decimal IcmsStFcpValorTotal { get; set; }
        public decimal IcmsStFcpRetidoValorTotal { get; set; }
        public decimal ValorFrete { get; set; }
        public decimal ValorSeguro { get; set; }
        public decimal ValorDesconto { get; set; }
        public decimal ValorImpostoImportacao { get; set; }
        public decimal ValorIpi { get; set; }
        public decimal IpiDevolucaoValorTotal { get; set; }
        public decimal PisValorTotal { get; set; }
        public decimal CofinsValorTotal { get; set; }
        public decimal ValorDespesas { get; set; }
        public decimal ValorTotalTributos { get; set; }
        public decimal IcmsFcpDestinoValorTotal { get; set; }
        public decimal FcpUfDestinoValorTotal { get; set; }
        public decimal IcmsFcpRemetenteValorTotal { get; set; }
        public decimal QuantidadeBcMonoRetido { get; set; }
        public decimal ValorIcmsMonoRetido { get; set; }
    }
}
