﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Extensions;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Pis
{
    [XmlRoot(ElementName = "PISOutr")]
    public class PisOutrasOperacoesXml
    {
        [XmlElement(ElementName = "CST")]
        public string Cst { get; set; }

        [XmlElement(ElementName = "vBC")]
        public string ValorBaseCalculo { get; set; }

        [XmlElement(ElementName = "pPIS")]
        public string AliquotaPercentual { get; set; }

        [XmlElement(ElementName = "qBCProd")]
        public string QuantidadeVendida { get; set; }

        [XmlElement(ElementName = "vAliqProd")]
        public string AliquotaReais { get; set; }

        [XmlElement(ElementName = "vPIS")]
        public string Valor { get; set; }

        public static PisOutrasOperacoesXml ConverterXml(PisCofinsCst cst, decimal baseCalculo, decimal aliquota, decimal valor)
        {
            var outrasOperacoesXml = new PisOutrasOperacoesXml
            {
                Cst = cst.FormatarValorEnumXmlNotaFiscal(),
                Valor = FormatarValor.FormatarValorXmlNotaFiscal(valor)
            };

            if (baseCalculo > 0)
            {
                outrasOperacoesXml.ValorBaseCalculo = FormatarValor.FormatarValorXmlNotaFiscal(baseCalculo);
                outrasOperacoesXml.AliquotaPercentual = FormatarValor.FormatarValorXmlNotaFiscal(aliquota, 4);
            }
            else
            {
                outrasOperacoesXml.QuantidadeVendida = FormatarValor.FormatarValorXmlNotaFiscal(0, 4);
                outrasOperacoesXml.AliquotaReais = FormatarValor.FormatarValorXmlNotaFiscal(0, 4);
            }

            return outrasOperacoesXml;
        }
    }
}
