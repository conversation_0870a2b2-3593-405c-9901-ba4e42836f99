﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Classes.Autorizar;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.EnderecosRetiradaEntrega
{
    [XmlRoot(ElementName = "entrega")]
    public class EntregaXml
    {
        [XmlElement(ElementName = "CNPJ")]
        public string Cnpj { get; set; }

        [XmlElement(ElementName = "CPF")]
        public string Cpf { get; set; }

        [XmlElement(ElementName = "xNome")]
        public string Nome { get; set; }

        [XmlElement(ElementName = "xLgr")]
        public string Logradouro { get; set; }

        [XmlElement(ElementName = "nro")]
        public string Numero { get; set; }

        [XmlElement(ElementName = "xCpl")]
        public string Complemento { get; set; }

        [XmlElement(ElementName = "xBairro")]
        public string Bairro { get; set; }

        [XmlElement(ElementName = "cMun")]
        public string CodMunicipio { get; set; }

        [XmlElement(ElementName = "xMun")]
        public string NomeMunicipio { get; set; }

        [XmlElement(ElementName = "UF")]
        public string SiglaUF { get; set; }

        [XmlElement(ElementName = "CEP")]
        public string Cep { get; set; }

        [XmlElement(ElementName = "cPais")]
        public string CodPais { get; set; }

        [XmlElement(ElementName = "xPais")]
        public string NomePais { get; set; }

        [XmlElement(ElementName = "fone")]
        public string Fone { get; set; }

        [XmlElement(ElementName = "email")]
        public string Email { get; set; }

        [XmlElement(ElementName = "IE")]
        public string InscricaoEstadual { get; set; }


        public static EntregaXml ConverterXml(Endereco endereco)
        {
            if (endereco == null)
            {
                return null;
            }

            var retiradaXml = new EntregaXml
            {
                Cnpj = !string.IsNullOrEmpty(endereco.Cnpj)
                        ? FormatarTexto.ManterSomenteNumeros(endereco.Cnpj)
                        : null,

                Cpf = !string.IsNullOrEmpty(endereco.Cpf)
                        ? FormatarTexto.ManterSomenteNumeros(endereco.Cpf)
                        : null,

                Nome = FormatarTexto.RemoverEspacos(endereco.RazaoSocial),
                Email = FormatarTexto.RemoverEspacos(endereco.Email),
                InscricaoEstadual = FormatarTexto.ManterSomenteNumeros(endereco.InscricaoEstadual),
                Logradouro = FormatarTexto.ManterSomenteNumerosELetras(endereco.Logradouro),
                Numero = FormatarTexto.ManterSomenteNumerosELetras(endereco.Numero),
                Complemento = FormatarTexto.ManterSomenteNumerosELetras(endereco.Complemento),
                Bairro = FormatarTexto.ManterSomenteNumerosELetras(endereco.Bairro),
                CodMunicipio = FormatarTexto.ManterSomenteNumeros(endereco.CodigoIbge),
                NomeMunicipio = FormatarTexto.ManterSomenteNumerosELetras(endereco.Cidade),
                SiglaUF = FormatarTexto.ManterSomenteNumerosELetras(endereco.SiglaUf),
                Cep = FormatarTexto.ManterSomenteNumeros(endereco.Cep),
                CodPais = FormatarTexto.ManterSomenteNumeros(endereco.CodigoPais),
                NomePais = FormatarTexto.ManterSomenteNumerosELetras(endereco.Pais),
                Fone = FormatarTexto.ManterSomenteNumeros(endereco.Telefone)
            };

            return retiradaXml;
        }
    }
}
