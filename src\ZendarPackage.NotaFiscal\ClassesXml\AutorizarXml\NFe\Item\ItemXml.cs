﻿using System.Collections.Generic;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Classes.Autorizar;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Produto;
using ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.TributosDevolvidos;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item
{
    [XmlRoot(ElementName = "det")]
    public class ItemXml
    {
        [XmlAttribute("nItem")]
        public int NumeroItem { get; set; }

        [XmlElement(ElementName = "prod")]
        public ProdutoXml Produto { get; set; }

        [XmlElement(ElementName = "imposto")]
        public ImpostosXml Impostos { get; set; }

        [XmlElement(ElementName = "impostoDevol")]
        public InformacoesTributosDevolvidosXml ImpostosDevolvidos { get; set; }

        [XmlElement(ElementName = "infAdProd")]
        public string InformacoesAdicionais { get; set; }

        public static ItemXml[] ConverterXml(InformacoesAutorizacao informacoesAutorizacao)
        {
            var listaProdutoXml = new List<ItemXml>();

            foreach (var item in informacoesAutorizacao.Itens)
            {
                listaProdutoXml.Add(new ItemXml
                {
                    NumeroItem = item.NumeroItem,
                    Produto = ProdutoXml.ConverterXml(item, informacoesAutorizacao.AmbienteFiscal, informacoesAutorizacao.ModeloFiscal, informacoesAutorizacao.NumeroPedidoExterno),
                    Impostos = ImpostosXml.ConverterXml(item, informacoesAutorizacao),
                    ImpostosDevolvidos = InformacoesTributosDevolvidosXml.ConverterXml(item.IpiValorDevolvido, item.PercentualDevolvido),
                    InformacoesAdicionais = FormatarTexto.RemoverEnter(item.DadosAdicionais)
                });
            }

            return listaProdutoXml.ToArray();
        }
    }
}
