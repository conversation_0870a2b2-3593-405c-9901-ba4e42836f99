﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Pagamento
{
    [XmlRoot(ElementName = "fat")]
    public class FaturaXml
    {
        [XmlElement(ElementName = "nFat")]
        public string NumeroFatura { get; set; }

        [XmlElement(ElementName = "vOrig")]
        public string ValorOriginal { get; set; }

        [XmlElement(ElementName = "vDesc")]
        public string ValorDesconto { get; set; }

        [XmlElement(ElementName = "vLiq")]
        public string ValorLiquido { get; set; }

        public static FaturaXml ConverterXml(decimal valorTotal)
        {
            return new FaturaXml
            {
                NumeroFatura = "0",
                ValorOriginal = FormatarValor.FormatarValorXmlNotaFiscal(valorTotal),
                ValorDesconto = FormatarValor.FormatarValorXmlNotaFiscal(0),
                ValorLiquido = FormatarValor.FormatarValorXmlNotaFiscal(valorTotal)
            };
        }
    }
}
