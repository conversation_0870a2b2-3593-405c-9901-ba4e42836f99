﻿using System.Collections.Generic;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Classes.Autorizar;
using ZendarPackage.NotaFiscal.Helpers;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Produto.Importacao
{
    [XmlRoot(ElementName = "adi")]
    public class AdicoesXml
    {
        [XmlElement(ElementName = "nAdicao")]
        public int NumeroAdicao { get; set; }

        [XmlElement(ElementName = "nSeqAdic")]
        public int NumeroSequencialItem { get; set; }

        [XmlElement(ElementName = "cFabricante")]
        public string CodigoFabricante { get; set; }

        [XmlElement(ElementName = "vDescDI")]
        public string ValorDescontoItem { get; set; }

        [XmlElement(ElementName = "nDraw")]
        public string NumeroDrawback { get; set; }

        public static AdicoesXml[] ConverterXml(List<ItemAdicao> adicoes)
        {
            var listaAdicoesXml = new List<AdicoesXml>();

            foreach (var itemAdicao in adicoes)
            {
                listaAdicoesXml.Add(new AdicoesXml
                {
                    NumeroAdicao = itemAdicao.NumeroAdicao,
                    NumeroSequencialItem = itemAdicao.Item,
                    CodigoFabricante = FormatarTexto.RemoverEspacos(itemAdicao.CodigoFabricante),
                    ValorDescontoItem = itemAdicao.DescontoAdicao > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(itemAdicao.DescontoAdicao) : null
                });
            }

            return listaAdicoesXml.ToArray();
        }
    }
}
