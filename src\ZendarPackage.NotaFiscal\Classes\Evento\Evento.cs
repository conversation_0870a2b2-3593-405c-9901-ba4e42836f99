﻿using System;
using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.Classes.Evento
{
    public class Evento : Base
    {
        public int NumeroSequencial { get; set; } = 1;
        public TipoEvento TipoEvento { get; set; }
        public int CodigoUf { get; set; }
        public string ChaveAcesso { get; set; }
        public DateTime DataHoraEvento { get; set; }
        public string CpfCnpj { get; set; }
        public string Justificativa { get; set; }
        public string NumeroProtocolo { get; set; }
        public string Condicao { get; set; }
        public int TimeZoneOffSet { get; set; }
    }
}
