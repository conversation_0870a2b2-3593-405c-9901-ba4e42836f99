﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Icms
{
    [XmlRoot(ElementName = "ICMSST")]
    public class IcmsStXml
    {
        [XmlElement(ElementName = "orig")]
        public int Origem { get; set; }

        [XmlElement(ElementName = "CST")]
        public int Cst { get; set; }

        [XmlElement(ElementName = "vBCSTRet")]
        public string ValorBaseCalculoStEmitente { get; set; }

        [XmlElement(ElementName = "pST")]
        public string AliquotaConsumidorFinalRepasse { get; set; }

        [XmlElement(ElementName = "vICMSSubstituto")]
        public string ValorIcmsSubstituto { get; set; }

        [XmlElement(ElementName = "vICMSSTRet")]
        public string ValorIcmsStEmitente { get; set; }

        [XmlElement(ElementName = "vBCFCPSTRet")]
        public string ValorBaseCalculoFCPStRet { get; set; }

        [XmlElement(ElementName = "pFCPSTRet")]
        public string PercentualFCPStRet { get; set; }

        [XmlElement(ElementName = "vFCPSTRet")]
        public string ValorFCPStRet { get; set; }

        [XmlElement(ElementName = "vBCSTDest")]
        public string ValorBaseCalculoStDestino { get; set; }

        [XmlElement(ElementName = "vICMSSTDest")]
        public string ValorIcmsStDestino { get; set; }

        [XmlElement(ElementName = "pRedBCEfet")]
        public string ReducaoBaseCalculoEfet { get; set; }

        [XmlElement(ElementName = "vBCEfet")]
        public string ValorBaseCalculoEfet { get; set; }

        [XmlElement(ElementName = "pICMSEfet")]
        public string PercentualIcmsEfet { get; set; }

        [XmlElement(ElementName = "vICMSEfet")]
        public string ValorIcmsEfet { get; set; }

        public static IcmsStXml ConverterXml(Classes.Autorizar.Item item, ModeloFiscal modelo)
        {
            var icmsXml = new IcmsStXml
            {
                Origem = (int)item.CstOrigem,
                Cst = (int)item.CstCsosn,
                ValorIcmsSubstituto = modelo == ModeloFiscal.NFe ? FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsSubstituto) : null
            };

            if (item.FcpStRetidoAliquota > 0)
            {
                icmsXml.ValorBaseCalculoFCPStRet = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStRetidoBaseCalculo);
                icmsXml.PercentualFCPStRet = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStRetidoAliquota);
                icmsXml.ValorFCPStRet = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStRetidoValor);
            }

            if (item.IcmsEfetivoBaseCalculo > 0 && item.IcmsEfetivoValor > 0)
            {
                icmsXml.ReducaoBaseCalculoEfet = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsEfetivoReducao);
                icmsXml.ValorBaseCalculoEfet = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsEfetivoBaseCalculo);
                icmsXml.PercentualIcmsEfet = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsEfetivoAliquota);
                icmsXml.ValorIcmsEfet = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsEfetivoValor);
            }

            return icmsXml;
        }
    }
}
