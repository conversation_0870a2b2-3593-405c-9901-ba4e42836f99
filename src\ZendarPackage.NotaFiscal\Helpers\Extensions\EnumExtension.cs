﻿using System;
using System.ComponentModel;

namespace ZendarPackage.NotaFiscal.Helpers.Extensions
{
    internal static class EnumExtension
    {
        private static T ObterAtributoDoTipo<T>(this Enum valorEnum) where T : Attribute
        {
            var type = valorEnum.GetType();
            var memInfo = type.GetMember(valorEnum.ToString());
            var attributes = memInfo[0].GetCustomAttributes(typeof(T), false);
            return (attributes.Length > 0) ? (T)attributes[0] : null;
        }

        public static string ObterDescricao(this Enum valorEnum)
        {
            return valorEnum.ObterAtributoDoTipo<DescriptionAttribute>().Description;
        }

        public static string FormatarValorEnumXmlNotaFiscal(this Enum valorEnum)
        {
            var valor = Convert.ToInt32(valorEnum);
            if (valor < 10)
            {
                return $"0{valor}";
            }

            return valor.ToString();
        }
    }
}
