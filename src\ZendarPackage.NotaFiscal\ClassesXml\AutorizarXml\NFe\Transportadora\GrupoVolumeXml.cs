﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Transportadora
{
    [XmlRoot(ElementName = "vol")]
    public class GrupoVolumeXml
    {
        [XmlElement(ElementName = "qVol")]
        public long QuantidadeVolumes { get; set; }

        [XmlElement(ElementName = "esp")]
        public string Especie { get; set; }

        [XmlElement(ElementName = "marca")]
        public string Marca { get; set; }

        [XmlElement(ElementName = "nVol")]
        public string NumeracaoVolumes { get; set; }

        [XmlElement(ElementName = "pesoL")]
        public string PesoLiquido { get; set; }

        [XmlElement(ElementName = "pesoB")]
        public string PesoBruto { get; set; }

        [XmlElement(ElementName = "lacre")]
        public GrupoLacresXml[] GrupoLacresXml { get; set; }

        public static GrupoVolumeXml ConverterXml(Classes.Autorizar.Transportadora transportadora)
        {
            return new GrupoVolumeXml
            {
                QuantidadeVolumes = transportadora.QuantidadeVolume,
                Especie = FormatarTexto.RemoverEspacos(transportadora.EspecieVolume),
                Marca = FormatarTexto.RemoverEspacos(transportadora.MarcaVolume),
                NumeracaoVolumes = FormatarTexto.RemoverEspacos(transportadora.NumeracaoVolume),
                PesoLiquido = FormatarValor.FormatarValorXmlNotaFiscal(transportadora.PesoLiquido, 3),
                PesoBruto = FormatarValor.FormatarValorXmlNotaFiscal(transportadora.PesoBruto, 3)
            };
        }
    }
}
