﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Transportadora
{
    [XmlRoot(ElementName = "veicTransp")]
    public class VeiculoTransportadoXml
    {
        [XmlElement(ElementName = "placa")]
        public string Placa { get; set; }

        [XmlElement(ElementName = "UF")]
        public string SiglaUF { get; set; }

        [XmlElement(ElementName = "RNTC")]
        public string Rntc { get; set; }

        public static VeiculoTransportadoXml ConverterXml(Classes.Autorizar.Transportadora transportadora, DestinoOperacao destinoOperacao)
        {
            if (!string.IsNullOrEmpty(transportadora.Placa) &&
                destinoOperacao != DestinoOperacao.OPERACAO_INTERESTADUAL)
            {
                return new VeiculoTransportadoXml
                {
                    Placa = FormatarTexto.RemoverEspacos(transportadora.Placa),
                    SiglaUF = FormatarTexto.RemoverEspacos(transportadora.UfVeiculo),
                    Rntc = FormatarTexto.RemoverEspacos(transportadora.Rntc)
                };
            }

            return null;
        }
    }
}
