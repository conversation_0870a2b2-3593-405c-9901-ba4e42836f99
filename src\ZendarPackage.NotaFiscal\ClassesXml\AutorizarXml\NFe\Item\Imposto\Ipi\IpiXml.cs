﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Extensions;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Ipi
{
    [XmlRoot(ElementName = "IPI")]
    public class IpiXml
    {
        [XmlElement(ElementName = "CNPJProd")]
        public string CnpjProdutor { get; set; }

        [XmlElement(ElementName = "cSelo")]
        public string CodigoSelo { get; set; }

        [XmlElement(ElementName = "qSelo")]
        public string QuantidadeSelo { get; set; }

        [XmlElement(ElementName = "cEnq")]
        public int CodigoEnquadramentoLegal { get; set; }

        [XmlElement(ElementName = "IPITrib")]
        public IpiTributarioXml IpiTributario { get; set; }

        [XmlElement(ElementName = "IPINT")]
        public IpiNtXml IpiNt { get; set; }

        public static IpiXml ConverterXml(IpiCst? cst, decimal baseCalculo, decimal aliquota, decimal valor, int enquadramentoLegal)
        {
            if (!cst.HasValue)
            {
                return null;
            }

            var grupoIpi = new IpiXml
            {
                CodigoEnquadramentoLegal = enquadramentoLegal
            };

            switch (cst.Value)
            {
                case IpiCst.ENTRADA_RECUPERACAO_CREDITO:
                case IpiCst.OUTRAS_ENTRADAS:
                case IpiCst.SAIDA_TRIBUTADA:
                case IpiCst.OUTRAS_SAIDAS:
                    grupoIpi.IpiTributario = IpiTributarioXml.ConverterXml(cst.Value, baseCalculo, aliquota, valor);
                    break;
                case IpiCst.ENTRADA_TRIBUTADA_ALIQUOTA_ZERO:
                case IpiCst.ENTRADA_ISENTA:
                case IpiCst.ENTRADA_NAO_TRIBUTADA:
                case IpiCst.ENTRADA_IMUNE:
                case IpiCst.ENTRADA_COM_SUSPENSAO:
                case IpiCst.SAIDA_TRIBUTADA_ALIQUOTA_ZERO:
                case IpiCst.SAIDA_ISENTA:
                case IpiCst.SAIDA_NAO_TRIBUTADA:
                case IpiCst.SAIDA_IMUNE:
                case IpiCst.SAIDA_COM_SUSPENSAO:
                    grupoIpi.IpiNt = new IpiNtXml
                    {
                        Cst = cst.FormatarValorEnumXmlNotaFiscal()
                    };
                    break;
                default:
                    break;
            }

            return grupoIpi;
        }
    }
}
