﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Extensions;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Pis
{
    [XmlRoot(ElementName = "PISQtde")]
    public class PisTributadoQuantidadeXml
    {
        [XmlElement(ElementName = "CST")]
        public string Cst { get; set; }

        [XmlElement(ElementName = "qBCProd")]
        public string QuantidadeVendida { get; set; }

        [XmlElement(ElementName = "vAliqProd")]
        public string AliquotaReais { get; set; }

        [XmlElement(ElementName = "vPIS")]
        public string Valor { get; set; }

        public static PisTributadoQuantidadeXml ConverterXml(PisCofinsCst cst, decimal valor)
        {
            return new PisTributadoQuantidadeXml
            {
                Cst = cst.FormatarValorEnumXmlNotaFiscal(),
                QuantidadeVendida = FormatarValor.FormatarValorXmlNotaFiscal(0, 4),
                AliquotaReais = FormatarValor.FormatarValorXmlNotaFiscal(0, 4),
                Valor = FormatarValor.FormatarValorXmlNotaFiscal(valor)
            };
        }
    }
}
