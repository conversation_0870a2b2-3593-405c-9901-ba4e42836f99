﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Produto.Exportacao
{
    [XmlRoot(ElementName = "exportInd")]
    public class GrupoExportacaoIndiretaXml
    {
        [XmlElement(ElementName = "nRE")]
        public string NumeroRegistroExportacao { get; set; }

        [XmlElement(ElementName = "chNFe")]
        public string ChaveAcesso { get; set; }

        [XmlElement(ElementName = "qExport")]
        public string QtdeItemRealmenteExportada { get; set; }
    }
}
