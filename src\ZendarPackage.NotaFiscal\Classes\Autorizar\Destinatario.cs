﻿using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.Classes.Autorizar
{
    public class Destinatario
    {
        public string CpfCnpj { get; set; }
        public string DocumentoEstrangeiro { get; set; }
        public string Nome { get; set; }
        public IndicadorIe IndicadorIe { get; set; }
        public string InscricaoEstadual { get; set; }
        public string Suframa { get; set; }
        public string Email { get; set; }
        public string Cep { get; set; }
        public string Logradouro { get; set; }
        public string Numero { get; set; }
        public string Complemento { get; set; }
        public string Bairro { get; set; }
        public string Cidade { get; set; }
        public string CodigoIbge { get; set; }
        public string SiglaUf { get; set; }
        public string CodigoPais { get; set; }
        public string Pais { get; set; }
        public string Telefone { get; set; }
    }
}
