﻿using System.Collections.Generic;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Classes.Autorizar;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Identificacao
{
    [XmlRoot(ElementName = "NFref")]
    public class GrupoNotaFiscalReferenciadaXml
    {
        [XmlElement(ElementName = "refNFe")]
        public string ChaveAcessoNFeReferenciada { get; set; }

        [XmlElement(ElementName = "refNF")]
        public NotaFiscalReferenciadaXml NotaFiscalReferenciadaXml { get; set; }

        [XmlElement(ElementName = "refCTe")]
        public string ChaveAcessoCTeReferenciada { get; set; }

        [XmlElement(ElementName = "refECF")]
        public EmissorCupomFiscalXml EmissorCupomFiscalXml { get; set; }


        public static GrupoNotaFiscalReferenciadaXml[] ConverterXml(InformacoesAutorizacao informacoesAutorizacao)
        {
            if (informacoesAutorizacao.ModeloFiscal == ModeloFiscal.NFCe)
            {
                return null;
            }

            var notasReferenciadas = new List<GrupoNotaFiscalReferenciadaXml>();

            foreach (var notaReferenciada in informacoesAutorizacao.DocumentosReferenciados)
            {
                var nota = new GrupoNotaFiscalReferenciadaXml();

                if (notaReferenciada.ModeloFiscal == ModeloFiscal.NF || notaReferenciada.ModeloFiscal == ModeloFiscal.NF_VENDA_CONSUMIDOR)
                {
                    nota.NotaFiscalReferenciadaXml = new NotaFiscalReferenciadaXml
                    {
                        CodigoUF = notaReferenciada.CodigoUf,
                        AnoMesEmissao = FormatarTexto.RemoverEspacos(notaReferenciada.AnoMesEmissao),
                        Cnpj = FormatarTexto.ManterSomenteNumeros(notaReferenciada.Cnpj),
                        Modelo = (int)notaReferenciada.ModeloFiscal,
                        Serie = notaReferenciada.Serie,
                        Numero = FormatarTexto.ManterSomenteNumeros(notaReferenciada.Numero)
                    };
                }
                else
                {
                    nota.ChaveAcessoNFeReferenciada = FormatarTexto.ManterSomenteNumeros(notaReferenciada.ChaveAcesso);
                }

                notasReferenciadas.Add(nota);
            }

            return notasReferenciadas.ToArray();
        }
    }
}
