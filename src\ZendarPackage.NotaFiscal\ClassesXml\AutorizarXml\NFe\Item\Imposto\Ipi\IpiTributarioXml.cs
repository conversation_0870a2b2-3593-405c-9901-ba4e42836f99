﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Extensions;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Ipi
{
    [XmlRoot(ElementName = "IPITrib")]
    public class IpiTributarioXml
    {
        [XmlElement(ElementName = "CST")]
        public string Cst { get; set; }

        [XmlElement(ElementName = "vBC")]
        public string ValorBaseCalculo { get; set; }

        [XmlElement(ElementName = "pIPI")]
        public string Aliquota { get; set; }

        [XmlElement(ElementName = "qUnid")]
        public string Quantidade { get; set; }

        [XmlElement(ElementName = "vUnid")]
        public string ValorUnitario { get; set; }

        [XmlElement(ElementName = "vIPI")]
        public string ValorIPI { get; set; }

        public static IpiTributarioXml ConverterXml(IpiCst cst, decimal baseCalculo, decimal aliquota, decimal valor)
        {
            return new IpiTributarioXml
            {
                Cst = cst.FormatarValorEnumXmlNotaFiscal(),
                ValorBaseCalculo = FormatarValor.FormatarValorXmlNotaFiscal(baseCalculo),
                Aliquota = FormatarValor.FormatarValorXmlNotaFiscal(aliquota),
                ValorIPI = FormatarValor.FormatarValorXmlNotaFiscal(valor)
            };
        }
    }
}
