﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Icms
{
    [XmlRoot(ElementName = "ICMS51")]
    public class Icms51Xml
    {
        [XmlElement(ElementName = "orig")]
        public int Origem { get; set; }

        [XmlElement(ElementName = "CST")]
        public int Cst { get; set; }

        [XmlElement(ElementName = "modBC")]
        public int ModalidadeBaseCalculoIcms { get; set; }

        [XmlElement(ElementName = "pRedBC")]
        public string ReducaoBaseCalculoIcms { get; set; }

        [XmlElement(ElementName = "vBC")]
        public string ValorBaseCalculo { get; set; }

        [XmlElement(ElementName = "pICMS")]
        public string Aliquota { get; set; }

        [XmlElement(ElementName = "vICMSOp")]
        public string ValorIcmsOperacao { get; set; }

        [XmlElement(ElementName = "pDif")]
        public string PercentualDiferimento { get; set; }

        [XmlElement(ElementName = "vICMSDif")]
        public string ValorIcmsDiferido { get; set; }

        [XmlElement(ElementName = "vICMS")]
        public string Valor { get; set; }

        [XmlElement(ElementName = "vBCFCP")]
        public string ValorBaseCalculoFCP { get; set; }

        [XmlElement(ElementName = "pFCP")]
        public string PercentualFCP { get; set; }

        [XmlElement(ElementName = "vFCP")]
        public string ValorFCP { get; set; }

        public static Icms51Xml ConverterXml(Classes.Autorizar.Item item)
        {
            var icmsXml = new Icms51Xml
            {
                Origem = (int)item.CstOrigem,
                Cst = (int)item.CstCsosn,
                ModalidadeBaseCalculoIcms = (int)item.ModalidadeIcms,
                ReducaoBaseCalculoIcms = item.IcmsReducaoBaseCalculo > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsReducaoBaseCalculo) : null,
                ValorBaseCalculo = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsBaseCalculo),
                Aliquota = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsAliquota),
                ValorIcmsOperacao = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsValorOperacao),
                PercentualDiferimento = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsDiferidoPercentual),
                ValorIcmsDiferido = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsDiferidoValor),
                Valor = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsValor)
            };

            if (item.FcpStAliquota > 0)
            {
                icmsXml.ValorBaseCalculoFCP = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpBaseCalculo);
                icmsXml.PercentualFCP = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpAliquota);
                icmsXml.ValorFCP = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpValor);
            }

            return icmsXml;
        }
    }
}
