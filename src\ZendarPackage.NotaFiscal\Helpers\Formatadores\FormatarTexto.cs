﻿using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;

namespace ZendarPackage.NotaFiscal.Helpers.Formatadores
{
    internal static class FormatarTexto
    {
        public static string ManterSomenteNumerosELetras(string texto)
        {
            if (!string.IsNullOrEmpty(texto))
            {
                texto = texto.Replace("", "")
                             .Replace("\a", "")
                             .Replace("\b", "")
                             .Replace("\r", "")
                             .Replace("\n", "")
                             .Replace("ª", "a")
                             .Replace("º", "o")
                             .Replace("&", "E");

                var textoSemAcento = new StringBuilder();
                var arrayText = texto.Normalize(NormalizationForm.FormD).ToCharArray();
                foreach (char letter in arrayText)
                {
                    if (CharUnicodeInfo.GetUnicodeCategory(letter) != UnicodeCategory.NonSpacingMark)
                        textoSemAcento.Append(letter);
                }

                texto = textoSemAcento.ToString();

                texto = Regex.Replace(texto, "[^0-9a-zA-Z_ ]", "");

                texto = texto.Trim();
            }

            return texto;
        }

        public static string ManterSomenteNumeros(string texto)
        {
            if (!string.IsNullOrEmpty(texto))
            {
                texto = Regex.Replace(texto, "[^0-9_ ]", "");
                texto = texto.Replace(" ", "");
            }

            return texto;
        }

        public static string TratarTextoVazio(string texto)
        {
            if (string.IsNullOrEmpty(texto)) return null;

            return texto;
        }

        public static string RemoverEspacos(string texto)
        {
            if (!string.IsNullOrEmpty(texto))
                texto = texto.Trim();

            return texto;
        }

        public static string RemoverEnter(string texto)
        {
            if (!string.IsNullOrEmpty(texto))
            {
                texto = texto.Trim();
                texto = texto.Replace("\n", " ");
            }

            return texto;
        }
    }
}
