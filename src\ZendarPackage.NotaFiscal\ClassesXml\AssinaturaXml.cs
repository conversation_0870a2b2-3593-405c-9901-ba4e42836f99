﻿using System.Collections.Generic;
using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml
{
    [XmlRoot(ElementName = "Signature")]
    public class AssinaturaXml
    {
        [XmlElement(ElementName = "SignedInfo")]
        public SignedInfoXML SignedInfoXML { get; set; }

        [XmlElement(ElementName = "SignatureValue")]
        public string SignatureValue { get; set; }

        [XmlElement(ElementName = "KeyInfo")]
        public KeyInfoXML KeyInfoXML { get; set; }
    }

    [XmlRoot(ElementName = "SignedInfo")]
    public class SignedInfoXML
    {
        [XmlElement(ElementName = "CanonicalizationMethod")]
        public CanonicalizationMethodXML CanonicalizationMethodXML { get; set; }

        [XmlElement(ElementName = "SignatureMethod")]
        public SignatureMethodXML SignatureMethodXML { get; set; }

        [XmlElement(ElementName = "Reference")]
        public ReferenceXML ReferenceXML { get; set; }
    }

    [XmlRoot(ElementName = "CanonicalizationMethod")]
    public class CanonicalizationMethodXML
    {
        [XmlAttribute("Algorithm")]
        public string Algorithm { get; set; }
    }

    [XmlRoot(ElementName = "SignatureMethod")]
    public class SignatureMethodXML
    {
        [XmlAttribute("Algorithm")]
        public string Algorithm { get; set; }
    }

    [XmlRoot(ElementName = "Reference")]
    public class ReferenceXML
    {
        [XmlAttribute("URI")]
        public string Uri { get; set; }

        [XmlElement(ElementName = "Transforms")]
        public List<TransformsXml> TransformXML { get; set; }

        [XmlElement(ElementName = "DigestMethod")]
        public DigestMethodXML DigestMethodXML { get; set; }

        [XmlElement(ElementName = "DigestValue")]
        public string DigestValue { get; set; }

    }

    [XmlRoot(ElementName = "Transforms")]
    public class TransformsXml
    {
        [XmlElement(ElementName = "Transform")]
        public List<TransformXML> Transform { get; set; }
    }

    [XmlRoot(ElementName = "Transform")]
    public class TransformXML
    {
        [XmlAttribute("Algorithm")]
        public string Algorithm { get; set; }
    }

    [XmlRoot(ElementName = "DigestMethod")]
    public class DigestMethodXML
    {
        [XmlAttribute("Algorithm")]
        public string Algorithm { get; set; }
    }

    [XmlRoot(ElementName = "KeyInfo")]
    public class KeyInfoXML
    {
        [XmlElement(ElementName = "X509Data")]
        public X509DataXML X509DataXML { get; set; }
    }

    [XmlRoot(ElementName = "X509Data")]
    public class X509DataXML
    {
        [XmlElement(ElementName = "X509Certificate")]
        public string X509Certificate { get; set; }
    }
}
