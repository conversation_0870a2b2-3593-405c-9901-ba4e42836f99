﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Extensions;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Pis
{
    [XmlRoot(ElementName = "PISAliq")]
    public class PisTributadoAliquotaXml
    {
        [XmlElement(ElementName = "CST")]
        public string Cst { get; set; }

        [XmlElement(ElementName = "vBC")]
        public string ValorBaseCalculo { get; set; }

        [XmlElement(ElementName = "pPIS")]
        public string AliquotaPercentual { get; set; }

        [XmlElement(ElementName = "vPIS")]
        public string Valor { get; set; }

        public static PisTributadoAliquotaXml ConverterXml(PisCofinsCst cst, decimal baseCalculo, decimal aliquota, decimal valor)
        {
            return new PisTributadoAliquotaXml
            {
                Cst = cst.FormatarValorEnumXmlNotaFiscal(),
                ValorBaseCalculo = FormatarValor.FormatarValorXmlNotaFiscal(baseCalculo),
                AliquotaPercentual = FormatarValor.FormatarValorXmlNotaFiscal(aliquota, 4),
                Valor = FormatarValor.FormatarValorXmlNotaFiscal(valor)
            };
        }
    }
}
