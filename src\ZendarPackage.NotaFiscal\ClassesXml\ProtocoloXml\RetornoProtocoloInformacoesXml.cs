﻿using System.Xml.Serialization;

namespace ZendarPackage.NotaFiscal.ClassesXml.ProtocoloXml
{
    public class RetornoProtocoloInformacoesXml
    {
        [XmlAttribute("id")]
        public string Id { get; set; }

        [XmlElement(ElementName = "tpAmb", Order = 1, IsNullable = false)]
        public int TipoAmbiente { get; set; }

        [XmlElement(ElementName = "verAplic", Order = 2, IsNullable = false)]
        public string VersaoAplicativo { get; set; }

        [XmlElement(ElementName = "chNFe", Order = 3, IsNullable = false)]
        public string ChaveAcesso { get; set; }

        [XmlElement(ElementName = "dhRecbto", Order = 4, IsNullable = false)]
        public string DataHoraRecebimento { get; set; }

        [XmlElement(ElementName = "nProt", Order = 5, IsNullable = false)]
        public string NumeroProtocolo { get; set; }

        [XmlElement(ElementName = "digVal", Order = 6, IsNullable = false)]
        public string DigestValue { get; set; }

        [XmlElement(ElementName = "cStat", Order = 7, IsNullable = false)]
        public int CodigoStatus { get; set; }

        [XmlElement(ElementName = "xMotivo", Order = 8, IsNullable = false)]
        public string Resposta { get; set; }

        [XmlElement(ElementName = "cMsg", Order = 9, IsNullable = false)]
        public string CodigoMensagem { get; set; }

        [XmlElement(ElementName = "xMsg", Order = 10, IsNullable = false)]
        public string Mensagem { get; set; }
    }
}
