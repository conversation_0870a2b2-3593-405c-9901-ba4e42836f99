﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Extensions;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Pagamento
{
    [XmlRoot(ElementName = "pag")]
    public class GrupoCartoesXml
    {
        [XmlElement(ElementName = "tpIntegra")]
        public int TpIntegra { get; set; }

        [XmlElement(ElementName = "CNPJ")]
        public string CnpjCredenciadora { get; set; }

        [XmlElement(ElementName = "tBand")]
        public string Bandeira { get; set; }

        [XmlElement(ElementName = "cAut")]
        public string NumeroAutorizacao { get; set; }

		[XmlElement(ElementName = "CNPJReceb")]
		public string CnpjBeneficiario { get; set; }

		[XmlElement(ElementName = "idTermPag")]
		public string IdTerminalPagamento { get; set; }

		public static GrupoCartoesXml ConverterXml(Classes.Autorizar.Pagamento pagamento)
        {
            if (pagamento.IntegracaoPagamento.HasValue)
            {
                return new GrupoCartoesXml
                {
                    TpIntegra = (int)pagamento.IntegracaoPagamento.Value,
                    CnpjCredenciadora = FormatarTexto.ManterSomenteNumeros(pagamento.CnpjCredenciadora),
                    Bandeira = pagamento.BandeiraCartao.HasValue ? pagamento.BandeiraCartao.FormatarValorEnumXmlNotaFiscal() : null,
                    NumeroAutorizacao = FormatarTexto.RemoverEspacos(pagamento.NumeroAutorizacao),
                    CnpjBeneficiario = FormatarTexto.ManterSomenteNumeros(pagamento.CnpjBeneficiario),
                    IdTerminalPagamento = FormatarTexto.RemoverEspacos(pagamento.TerminalPagamentoId)
                };
            }

            return null;
        }
    }
}
