﻿using System.Collections.Generic;
using System.Linq;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Classes.Autorizar;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Produto.Importacao
{
    [XmlRoot(ElementName = "DI")]
    public class DeclaracaoImportacaoXml
    {
        [XmlElement(ElementName = "nDI")]
        public string NumeroDocumentoImportacao { get; set; }

        [XmlElement(ElementName = "dDI")]
        public string DataRegistro { get; set; }

        [XmlElement(ElementName = "xLocDesemb")]
        public string LocalDesembaraco { get; set; }

        [XmlElement(ElementName = "UFDesemb")]
        public string SiglaUFDesembaraco { get; set; }

        [XmlElement(ElementName = "dDesemb")]
        public string DataDesembaraco { get; set; }

        [XmlElement(ElementName = "tpViaTransp")]
        public int TipoTransporte { get; set; }

        [XmlElement(ElementName = "vAFRMM")]
        public string ValorAdicionalFreteMaritimo { get; set; }

        [XmlElement(ElementName = "tpIntermedio")]
        public int TipoIntermedio { get; set; }

        [XmlElement(ElementName = "CNPJ")]
        public string CnpjEncomendante { get; set; }

        [XmlElement(ElementName = "UFTerceiro")]
        public string UfTerceiro { get; set; }

        [XmlElement(ElementName = "cExportador")]
        public string CodigoExportador { get; set; }

        [XmlElement(ElementName = "adi")]
        public AdicoesXml[] Adicoes { get; set; }

        public static DeclaracaoImportacaoXml[] ConverterXml(decimal impostoImportacao, List<ItemAdicao> adicoes)
        {
            if (impostoImportacao == 0)
            {
                return null;
            }

            var listaDeclaracaoImportacaoXml = new List<DeclaracaoImportacaoXml>();

            var adicoesAgrupadas = adicoes.GroupBy(a => a.ImportacaoId);

            foreach (var itemAdicao in adicoesAgrupadas)
            {
                var adicao = itemAdicao.FirstOrDefault();

                var declaracaoImportacaoXml = new DeclaracaoImportacaoXml
                {
                    NumeroDocumentoImportacao = FormatarTexto.RemoverEspacos(adicao.Importacao.NumeroDocumento),
                    DataRegistro = adicao.Importacao.DataRegistro.ToString("yyyy-MM-dd"),
                    LocalDesembaraco = FormatarTexto.RemoverEspacos(adicao.Importacao.LocalDesembaraco),
                    SiglaUFDesembaraco = FormatarTexto.RemoverEspacos(adicao.Importacao.UfDesembaraco),
                    DataDesembaraco = adicao.Importacao.DataDesembaraco.ToString("yyyy-MM-dd"),
                    TipoTransporte = (int)adicao.Importacao.ViaTransporte,
                    TipoIntermedio = (int)adicao.Importacao.FormaImportacao,
                    CodigoExportador = FormatarTexto.RemoverEspacos(adicao.Importacao.CodigoExportador),
                    Adicoes = AdicoesXml.ConverterXml(itemAdicao.ToList())
                };

                if (adicao.Importacao.ViaTransporte == ViaTransporte.MARITIMA)
                {
                    declaracaoImportacaoXml.ValorAdicionalFreteMaritimo = FormatarValor.FormatarValorXmlNotaFiscal(adicao.Importacao.ValorAfrmm);
                }

                if (adicao.Importacao.ViaTransporte == ViaTransporte.MARITIMA ||
                    adicao.Importacao.ViaTransporte == ViaTransporte.FLUVIAL)
                {
                    declaracaoImportacaoXml.CnpjEncomendante = FormatarTexto.RemoverEspacos(adicao.Importacao.CnpjAdquirente);
                    declaracaoImportacaoXml.UfTerceiro = FormatarTexto.RemoverEspacos(adicao.Importacao.UfAdquirente);
                }

                listaDeclaracaoImportacaoXml.Add(declaracaoImportacaoXml);
            }

            return listaDeclaracaoImportacaoXml.Count > 0 ? listaDeclaracaoImportacaoXml.ToArray() : null;
        }
    }
}
