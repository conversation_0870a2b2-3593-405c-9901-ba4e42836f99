﻿using System.Collections.Generic;
using System.Linq;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Icms
{
    [XmlRoot(ElementName = "ICMSSN500")]
    public class IcmsSn500Xml
    {
        [XmlElement(ElementName = "orig")]
        public int Origem { get; set; }

        [XmlElement(ElementName = "CSOSN")]
        public int Csosn { get; set; }

        [XmlElement(ElementName = "vBCSTRet")]
        public string ValorBaseCalculoStRetido { get; set; }

        [XmlElement(ElementName = "pST")]
        public string AliquotaConsumidorFinal { get; set; }

        [XmlElement(ElementName = "vICMSSubstituto")]
        public string ValorIcmsSubstituto { get; set; }

        [XmlElement(ElementName = "vICMSSTRet")]
        public string ValorIcmsStRetido { get; set; }

        [XmlElement(ElementName = "vBCFCPSTRet")]
        public string ValorBaseCalculoFCPSTRet { get; set; }

        [XmlElement(ElementName = "pFCPSTRet")]
        public string PercentualFCPSTRet { get; set; }

        [XmlElement(ElementName = "vFCPSTRet")]
        public string ValorFCPSTRet { get; set; }

        public static IcmsSn500Xml ConverterXml(Classes.Autorizar.Item item, ModeloFiscal modelo, List<string> regrasAtivas)
        {
            var icmsXml = new IcmsSn500Xml
            {
                Origem = (int)item.CstOrigem,
                Csosn = (int)item.CstCsosn,
                ValorIcmsSubstituto = modelo == ModeloFiscal.NFe
                                      ? FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsSubstituto)
                                      : null
            };

            //se a regra N12a-50 estiver ativa, os campos devem ser preenchidos independente do valor
            if (regrasAtivas.FirstOrDefault(r => r == "N12a-50") != null)
            {
                icmsXml.ValorBaseCalculoStRetido = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStRetidoBaseCalculo);
                icmsXml.ValorIcmsStRetido = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStRetidoValor);
                icmsXml.AliquotaConsumidorFinal = FormatarValor.FormatarValorXmlNotaFiscal(item.AliquotaConsumidorFinal);
            }
            else
            {
                icmsXml.ValorBaseCalculoStRetido = item.IcmsStRetidoBaseCalculo > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStRetidoBaseCalculo) : null;
                icmsXml.ValorIcmsStRetido = item.IcmsStRetidoValor > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStRetidoValor) : null;
                icmsXml.AliquotaConsumidorFinal = item.AliquotaConsumidorFinal > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.AliquotaConsumidorFinal) : null;
            }

            if (item.FcpStRetidoAliquota > 0)
            {
                icmsXml.ValorBaseCalculoFCPSTRet = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStRetidoBaseCalculo);
                icmsXml.PercentualFCPSTRet = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStRetidoAliquota);
                icmsXml.ValorFCPSTRet = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStRetidoValor);
            }

            return icmsXml;
        }
    }
}
