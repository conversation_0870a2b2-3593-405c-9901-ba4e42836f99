﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Icms
{
    [XmlRoot(ElementName = "ICMSSN101")]
    public class IcmsSn101Xml
    {
        [XmlElement(ElementName = "orig")]
        public int Origem { get; set; }

        [XmlElement(ElementName = "CSOSN")]
        public int Csosn { get; set; }

        [XmlElement(ElementName = "pCredSN")]
        public string AliquotaCredito { get; set; }

        [XmlElement(ElementName = "vCredICMSSN")]
        public string ValorCredito { get; set; }

        public static IcmsSn101Xml ConverterXml(Classes.Autorizar.Item item)
        {
            return new IcmsSn101Xml
            {
                Origem = (int)item.CstOrigem,
                Csosn = (int)item.CstCsosn,
                AliquotaCredito = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsAproveitamentoAliquota),
                ValorCredito = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsAproveitamentoValor)
            };
        }
    }
}
