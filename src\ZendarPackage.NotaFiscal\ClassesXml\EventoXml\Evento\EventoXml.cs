﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;
using ZendarPackage.NotaFiscal.Helpers;
using ZendarPackage.NotaFiscal.Helpers.Extensions;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;
using ZendarPackage.NotaFiscal.Helpers.Validacoes;

namespace ZendarPackage.NotaFiscal.ClassesXml.EventoXml.Evento
{
    [XmlRoot(ElementName = "evento", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class EventoXml
    {
        [XmlAttribute("versao")]
        public string Versao { get; set; }

        [XmlElement(ElementName = "infEvento", Order = 1, IsNullable = false)]
        public EventoInformacoesXml InformacoesEventoXml { get; set; }

        [XmlElement(ElementName = "Signature", Order = 2, IsNullable = false, Namespace = "http://www.w3.org/2000/09/xmldsig#")]
        public AssinaturaXml AssinaturaXml { get; set; }

        public static EventoXml ConverterXml(Classes.Evento.Evento evento)
        {
            var eventoXml = new EventoXml
            {
                Versao = evento.Versao,
                InformacoesEventoXml = new EventoInformacoesXml
                {
                    Id = $"ID{(int)evento.TipoEvento}{evento.ChaveAcesso}{evento.NumeroSequencial.ToString().PadLeft(2, '0')}",
                    NumeroSequencial = evento.NumeroSequencial,
                    ChaveAcesso = evento.ChaveAcesso,
                    CodigoOrgao = evento.CodigoUf,
                    TipoAmbiente = (int)evento.AmbienteFiscal,
                    VersaoEvento = evento.Versao,
                    DataHoraEvento = FormatarData.FormatarDataXmlNotaFiscal(evento.DataHoraEvento, evento.TimeZoneOffSet),
                    TipoEvento = (int)evento.TipoEvento,
                    DetalhesEventoXml = new EventoDetalhesXml
                    {
                        Versao = evento.Versao,
                        DescricaoEvento = evento.TipoEvento.ObterDescricao()
                    }
                }
            };

            if (ValidacaoCpfCnpj.ValidarCnpj(evento.CpfCnpj))
            {
                eventoXml.InformacoesEventoXml.Cnpj = evento.CpfCnpj;
            }
            else
            {
                eventoXml.InformacoesEventoXml.Cpf = evento.CpfCnpj;
            }

            if (evento.TipoEvento == TipoEvento.CANCELAMENTO)
            {
                eventoXml.InformacoesEventoXml.DetalhesEventoXml.Justificativa = FormatarTexto.TratarTextoVazio(evento.Justificativa);
                eventoXml.InformacoesEventoXml.DetalhesEventoXml.NumeroProtocolo = evento.NumeroProtocolo;
            }
            else if (evento.TipoEvento == TipoEvento.CARTA_CORRECAO)
            {
                eventoXml.InformacoesEventoXml.DetalhesEventoXml.Correcao = FormatarTexto.TratarTextoVazio(evento.Justificativa);
                eventoXml.InformacoesEventoXml.DetalhesEventoXml.Condicao = evento.Condicao;
            }

            return eventoXml;
        }
    }
}
