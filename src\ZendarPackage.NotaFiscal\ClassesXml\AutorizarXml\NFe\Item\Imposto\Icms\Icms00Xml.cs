﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Extensions;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Icms
{
    [XmlRoot(ElementName = "ICMS00")]
    public class Icms00Xml
    {
        [XmlElement(ElementName = "orig")]
        public int Origem { get; set; }

        [XmlElement(ElementName = "CST")]
        public string Cst { get; set; }

        [XmlElement(ElementName = "modBC")]
        public int ModalidadeBaseCalculoIcms { get; set; }

        [XmlElement(ElementName = "vBC")]
        public string ValorBaseCalculo { get; set; }

        [XmlElement(ElementName = "pICMS")]
        public string Aliquota { get; set; }

        [XmlElement(ElementName = "vICMS")]
        public string Valor { get; set; }

        [XmlElement(ElementName = "pFCP")]
        public string PercentualFCP { get; set; }

        [XmlElement(ElementName = "vFCP")]
        public string ValorFCP { get; set; }

        public static Icms00Xml ConverterXml(Classes.Autorizar.Item item)
        {
            var icmsXml = new Icms00Xml
            {
                Origem = (int)item.CstOrigem,
                Cst = item.CstCsosn.FormatarValorEnumXmlNotaFiscal(),
                Aliquota = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsAliquota),
                ModalidadeBaseCalculoIcms = (int)item.ModalidadeIcms,
                ValorBaseCalculo = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsBaseCalculo),
                Valor = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsValor)
            };

            if (item.FcpAliquota > 0)
            {
                icmsXml.PercentualFCP = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpAliquota);
                icmsXml.ValorFCP = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpValor);
            }

            return icmsXml;
        }
    }
}
