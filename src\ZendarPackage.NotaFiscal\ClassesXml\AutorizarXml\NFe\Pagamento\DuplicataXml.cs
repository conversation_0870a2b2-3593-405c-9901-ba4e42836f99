﻿using System.Collections.Generic;
using System.Linq;
using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Pagamento
{
    [XmlRoot(ElementName = "dup")]
    public class DuplicataXml
    {
        [XmlElement(ElementName = "nDup")]
        public string Numero { get; set; }

        [XmlElement(ElementName = "dVenc")]
        public string DataVencto { get; set; }

        [XmlElement(ElementName = "vDup")]
        public string Valor { get; set; }

        public static DuplicataXml[] ConverterXml(List<Classes.Autorizar.Pagamento> pagamentos)
        {
            var listaDuplicataXml = new List<DuplicataXml>();

            foreach (var pagamento in pagamentos.OrderBy(p => p.Pa<PERSON>))
            {
                listaDuplicataXml.Add(new DuplicataXml
                {
                    Numero = FormatarTexto.RemoverEspacos(pagamento.Parcela),
                    DataVencto = pagamento.DataVencimento.ToString("yyyy-MM-dd"),
                    Valor = FormatarValor.FormatarValorXmlNotaFiscal(pagamento.Valor)
                });
            }

            return listaDuplicataXml.ToArray();
        }
    }
}
