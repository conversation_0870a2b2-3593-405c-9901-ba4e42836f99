﻿using System.Collections.Generic;
using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.Classes.Autorizar
{
    public class Item
    {
        public int NumeroItem { get; set; }
        public string CodigoProduto { get; set; }
        public string CodigoBarras { get; set; }
        public string DescricaoProduto { get; set; }
        public string Ncm { get; set; }
        public string CodigoCest { get; set; }
        public string IndicadorEscalaRelevante { get; set; }
        public string CnpjFabricante { get; set; }
        public string CodigoBeneficioFiscal { get; set; }
        public string Cfop { get; set; }
        public string UnidadeMedida { get; set; }
        public string UnidadeTributavel { get; set; }
        public string DadosAdicionais { get; set; }
        public decimal IpiValorDevolvido { get; set; }
        public decimal PercentualDevolvido { get; set; }
        public decimal Quantidade { get; set; }
        public decimal ValorUnitario { get; set; }
        public decimal ValorTotal { get; set; }
        public decimal QuantidadeTributavel { get; set; }
        public decimal ValorUnitarioTributavel { get; set; }
        public decimal ValorFrete { get; set; }
        public decimal ValorSeguro { get; set; }
        public decimal ValorDesconto { get; set; }
        public decimal ValorOutrasDespesas { get; set; }
        public IndicadorValorTotalNota IndicadorValorTotalNota { get; set; }
        public string NumeroPedidoCompra { get; set; }
        public int? ItemPedidoCompra { get; set; }
        public decimal ValorTotalTributos { get; set; }
        public decimal IcmsUfDestinoBaseCalculo { get; set; }
        public decimal FcpUfDestinoBaseCalculo { get; set; }
        public decimal FcpUfDestinoPercentual { get; set; }
        public decimal IcmsUfDestinoPercentual { get; set; }
        public decimal IcmsAliquotaInterestadual { get; set; }
        public decimal IcmsPercentualPartilha { get; set; }
        public decimal FcpUfDestinoValor { get; set; }
        public decimal IcmsUfDestinoValor { get; set; }
        public decimal IcmsUfRemetenteValor { get; set; }
        public PisCofinsCst CofinsCst { get; set; }
        public decimal CofinsBaseCalculo { get; set; }
        public decimal CofinsAliquota { get; set; }
        public decimal CofinsValor { get; set; }
        public PisCofinsCst PisCst { get; set; }
        public decimal PisBaseCalculo { get; set; }
        public decimal PisAliquota { get; set; }
        public decimal PisValor { get; set; }
        public decimal ImpostoImportacao { get; set; }
        public IpiCst? IpiCst { get; set; }
        public decimal IpiBaseCalculo { get; set; }
        public decimal IpiAliquota { get; set; }
        public decimal IpiValor { get; set; }
        public int EnquadramentoLegal { get; set; }
        public IcmsCstCsosn CstCsosn { get; set; }
        public string CodigoAnp { get; set; }
        public OrigemMercadoria CstOrigem { get; set; }
        public decimal IcmsAliquota { get; set; }
        public decimal IcmsBaseCalculo { get; set; }
        public decimal IcmsValor { get; set; }
        public decimal FcpAliquota { get; set; }
        public decimal FcpValor { get; set; }
        public ModalidadeBaseCalculoIcms ModalidadeIcms { get; set; }
        public ModalidadeBaseCalculoIcmsSt ModalidadeIcmsSt { get; set; }
        public decimal IcmsStMva { get; set; }
        public decimal IcmsStReducaoBaseCalculo { get; set; }
        public decimal IcmsStBaseCalculo { get; set; }
        public decimal IcmsStAliquota { get; set; }
        public decimal IcmsStValor { get; set; }
        public decimal FcpBaseCalculo { get; set; }
        public decimal FcpStBaseCalculo { get; set; }
        public decimal FcpStAliquota { get; set; }
        public decimal FcpStValor { get; set; }
        public decimal IcmsReducaoBaseCalculo { get; set; }
        public decimal IcmsDesoneradoValor { get; set; }
        public MotivoDesoneracaoIcms? IcmsMotivoDesoneracao { get; set; }
        public decimal IcmsValorOperacao { get; set; }
        public decimal IcmsDiferidoPercentual { get; set; }
        public decimal IcmsDiferidoValor { get; set; }
        public decimal IcmsSubstituto { get; set; }
        public decimal IcmsStRetidoBaseCalculo { get; set; }
        public decimal IcmsStRetidoValor { get; set; }
        public decimal AliquotaConsumidorFinal { get; set; }
        public decimal FcpStRetidoAliquota { get; set; }
        public decimal FcpStRetidoBaseCalculo { get; set; }
        public decimal FcpStRetidoValor { get; set; }
        public decimal IcmsAproveitamentoAliquota { get; set; }
        public decimal IcmsAproveitamentoValor { get; set; }
        public decimal IcmsEfetivoBaseCalculo { get; set; }
        public decimal IcmsEfetivoValor { get; set; }
        public decimal IcmsEfetivoReducao { get; set; }
        public decimal IcmsEfetivoAliquota { get; set; }
        public string DescricaoProdutoAnp { get; set; }
        public decimal PercentualGlp { get; set; }
        public decimal PercentualGasNacional { get; set; }
        public decimal PercentualGasImportado { get; set; }
        public decimal ValorPartidaGlp { get; set; }
        public string CodigoCodif { get; set; }
        public decimal QuantidadeFaturada { get; set; }
        public string UfConsumo { get; set; }
        public decimal CideBaseCalculo { get; set; }
        public decimal CideAliquota { get; set; }
        public decimal CideValor { get; set; }
        public decimal AliquotaAdREmICMSRetido { get; set; }
        public decimal QuantidadeBCMonoRetido { get; set; }
        public decimal ValorIcmsMonoRetido { get; set; }

        public List<ItemAdicao> Adicoes { get; set; }
        public List<ItemExportacao> Exportacoes { get; set; }
        public List<ItemOrigemCombustivel> ItemOrigemCombustivel { get; set; }
    }
}
