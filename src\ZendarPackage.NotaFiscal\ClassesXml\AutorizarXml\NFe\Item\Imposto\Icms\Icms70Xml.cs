﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Imposto.Icms
{
    [XmlRoot(ElementName = "ICMS70")]
    public class Icms70Xml
    {
        [XmlElement(ElementName = "orig")]
        public int Origem { get; set; }

        [XmlElement(ElementName = "CST")]
        public int Cst { get; set; }

        [XmlElement(ElementName = "modBC")]
        public int ModalidadeBaseCalculoIcms { get; set; }

        [XmlElement(ElementName = "pRedBC")]
        public string ReducaoBaseCalculoIcms { get; set; }

        [XmlElement(ElementName = "vBC")]
        public string ValorBaseCalculo { get; set; }

        [XmlElement(ElementName = "pICMS")]
        public string Aliquota { get; set; }

        [XmlElement(ElementName = "vICMS")]
        public string Valor { get; set; }

        [XmlElement(ElementName = "vBCFCP")]
        public string ValorBaseCalculoFCP { get; set; }

        [XmlElement(ElementName = "pFCP")]
        public string PercentualFCP { get; set; }

        [XmlElement(ElementName = "vFCP")]
        public string ValorFCP { get; set; }

        [XmlElement(ElementName = "modBCST")]
        public int ModalidadeBaseCalculoIcmsST { get; set; }

        [XmlElement(ElementName = "pMVAST")]
        public string MargemValorAdicionadoIcmsSt { get; set; }

        [XmlElement(ElementName = "pRedBCST")]
        public string ReducaoBaseCalculoIcmsSt { get; set; }

        [XmlElement(ElementName = "vBCST")]
        public string ValorBaseCalculoIcmsSt { get; set; }

        [XmlElement(ElementName = "pICMSST")]
        public string AliquotaIcmsSt { get; set; }

        [XmlElement(ElementName = "vICMSST")]
        public string ValorIcmsSt { get; set; }

        [XmlElement(ElementName = "vBCFCPST")]
        public string ValorBaseCalculoFCPST { get; set; }

        [XmlElement(ElementName = "pFCPST")]
        public string PercentualFCPST { get; set; }

        [XmlElement(ElementName = "vFCPST")]
        public string ValorFCPST { get; set; }

        [XmlElement(ElementName = "vICMSDeson")]
        public string ValorDesonerado { get; set; }

        [XmlElement(ElementName = "motDesICMS")]
        public string MotivoDesoneracao { get; set; }

        public static Icms70Xml ConverterXml(Classes.Autorizar.Item item)
        {
            var icmsXml = new Icms70Xml
            {
                Origem = (int)item.CstOrigem,
                Cst = (int)item.CstCsosn,
                ModalidadeBaseCalculoIcms = (int)item.ModalidadeIcms,
                ReducaoBaseCalculoIcms = item.IcmsReducaoBaseCalculo > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsReducaoBaseCalculo) : null,
                ValorBaseCalculo = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsBaseCalculo),
                Aliquota = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsAliquota),
                Valor = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsValor),
                ModalidadeBaseCalculoIcmsST = (int)item.ModalidadeIcmsSt,
                MargemValorAdicionadoIcmsSt = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStMva),
                ReducaoBaseCalculoIcmsSt = item.IcmsStReducaoBaseCalculo > 0 ? FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStReducaoBaseCalculo) : null,
                ValorBaseCalculoIcmsSt = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStBaseCalculo),
                AliquotaIcmsSt = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStAliquota),
                ValorIcmsSt = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsStValor)
            };

            if (item.FcpAliquota > 0)
            {
                icmsXml.ValorBaseCalculoFCP = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpBaseCalculo);
                icmsXml.PercentualFCP = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpAliquota);
                icmsXml.ValorFCP = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpValor);
            }

            if (item.FcpStAliquota > 0)
            {
                icmsXml.ValorBaseCalculoFCPST = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStBaseCalculo);
                icmsXml.PercentualFCPST = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStAliquota);
                icmsXml.ValorFCPST = FormatarValor.FormatarValorXmlNotaFiscal(item.FcpStValor);
            }

            if (item.IcmsMotivoDesoneracao.HasValue)
            {
                icmsXml.ValorDesonerado = FormatarValor.FormatarValorXmlNotaFiscal(item.IcmsDesoneradoValor);
                icmsXml.MotivoDesoneracao = item.IcmsMotivoDesoneracao.HasValue ? ((int)item.IcmsMotivoDesoneracao.Value).ToString() : "";
            }

            return icmsXml;
        }
    }
}
