﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Transportadora
{
    [XmlRoot(ElementName = "transp")]
    public class GrupoInformacoesTransporteXml
    {
        [XmlElement(ElementName = "modFrete")]
        public int ModFrete { get; set; }

        [XmlElement(ElementName = "transporta")]
        public TransportadorXml Transportador { get; set; }

        [XmlElement(ElementName = "retTransp")]
        public RetencaoTransporteXml RetencaoTransporte { get; set; }

        [XmlElement(ElementName = "veicTransp")]
        public VeiculoTransportadoXml VeiculoTransportado { get; set; }

        [XmlElement(ElementName = "reboque")]
        public ReboqueXml Reboque { get; set; }

        [XmlElement(ElementName = "vol")]
        public GrupoVolumeXml Volume { get; set; }

        public static GrupoInformacoesTransporteXml ConverterXml(Classes.Autorizar.Transportadora transportadora, DestinoOperacao destinoOperacao)
        {
            var informacoesTransporteXml = new GrupoInformacoesTransporteXml
            {
                ModFrete = (int)transportadora.ModalidadeFrete,
            };

            if (transportadora.ModalidadeFrete != ModalidadeFrete.SEM_FRETE)
            {
                informacoesTransporteXml.Transportador = TransportadorXml.ConverterXml(transportadora);
                informacoesTransporteXml.VeiculoTransportado = VeiculoTransportadoXml.ConverterXml(transportadora, destinoOperacao);
                informacoesTransporteXml.Volume = GrupoVolumeXml.ConverterXml(transportadora);
            }

            return informacoesTransporteXml;
        }
    }
}
