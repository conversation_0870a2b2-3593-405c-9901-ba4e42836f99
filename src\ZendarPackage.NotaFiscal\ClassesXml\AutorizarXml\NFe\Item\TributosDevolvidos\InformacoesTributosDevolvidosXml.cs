﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.TributosDevolvidos
{
    [XmlRoot(ElementName = "impostoDevol")]
    public class InformacoesTributosDevolvidosXml
    {
        [XmlElement(ElementName = "pDevol")]
        public string PercentualDevolvido { get; set; }

        [XmlElement(ElementName = "IPI")]
        public InformacoesIpiDevolvidosXml InformacoesIpiDevolvidos { get; set; }

        public static InformacoesTributosDevolvidosXml ConverterXml(decimal ipiValorDevolvido, decimal percentualDevolvido)
        {
            if (ipiValorDevolvido == 0)
            {
                return null;
            }

            return new InformacoesTributosDevolvidosXml
            {
                PercentualDevolvido = FormatarValor.FormatarValorXmlNotaFiscal(percentualDevolvido),
                InformacoesIpiDevolvidos = new InformacoesIpiDevolvidosXml
                {
                    ValorIPIDevolvido = FormatarValor.FormatarValorXmlNotaFiscal(ipiValorDevolvido)
                }
            };
        }
    }
}
