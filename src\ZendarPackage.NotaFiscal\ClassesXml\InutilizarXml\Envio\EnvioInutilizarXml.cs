﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Classes.Inutilizar;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.InutilizarXml.Envio
{
    [XmlRoot(ElementName = "inutNFe", Namespace = "http://www.portalfiscal.inf.br/nfe")]
    public class EnvioInutilizarXml
    {
        [XmlAttribute("versao")]
        public string Versao { get; set; }

        [XmlElement("infInut", Order = 1, IsNullable = false)]
        public EnvioInutilizarInformacoesXml InformacoesInutilizarXml { get; set; }

        [XmlElement(ElementName = "Signature", Order = 2, IsNullable = false)]
        public AssinaturaXml AssinaturaXml { get; set; }

        public static EnvioInutilizarXml ConverterXml(Inutilizar inutilizar)
        {
            var inutilizacaoXml = new EnvioInutilizarXml
            {
                Versao = inutilizar.Versao,
                InformacoesInutilizarXml = new EnvioInutilizarInformacoesXml
                {
                    CodigoUF = inutilizar.CodigoUf,
                    Modelo = inutilizar.ModeloFiscal,
                    TipoAmbiente = (int)inutilizar.AmbienteFiscal,
                    Ano = inutilizar.Ano,
                    Cnpj = FormatarTexto.ManterSomenteNumeros(inutilizar.Cnpj).PadLeft(14, '0'),
                    Serie = inutilizar.NumeroSerie,
                    NumeroNFeInicial = inutilizar.NumeroInicial,
                    NumeroNFeFinal = inutilizar.NumeroFinal,
                    Justificativa = FormatarTexto.TratarTextoVazio(inutilizar.Justificativa)
                }
            };

            inutilizacaoXml.InformacoesInutilizarXml.Id = $"ID{inutilizacaoXml.InformacoesInutilizarXml.CodigoUF}" +
                                                     $"{inutilizacaoXml.InformacoesInutilizarXml.Ano}" +
                                                     $"{inutilizacaoXml.InformacoesInutilizarXml.Cnpj}" +
                                                     $"{inutilizacaoXml.InformacoesInutilizarXml.Modelo}" +
                                                     $"{inutilizacaoXml.InformacoesInutilizarXml.Serie.ToString().PadLeft(3, '0')}" +
                                                     $"{inutilizacaoXml.InformacoesInutilizarXml.NumeroNFeInicial.ToString().PadLeft(9, '0')}" +
                                                     $"{inutilizacaoXml.InformacoesInutilizarXml.NumeroNFeFinal.ToString().PadLeft(9, '0')}";

            return inutilizacaoXml;
        }
    }
}
