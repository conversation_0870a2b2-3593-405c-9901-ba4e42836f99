﻿using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.Classes.Autorizar
{
    public class Emitente
    {
        public string Cnpj { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string InscricaoEstadual { get; set; }
        public RegimeTributario RegimeTributario { get; set; }
        public string Cep { get; set; }
        public string Logradouro { get; set; }
        public string Numero { get; set; }
        public string Complemento { get; set; }
        public string Bairro { get; set; }
        public string Cidade { get; set; }
        public string CodigoIbge { get; set; }
        public string SiglaUf { get; set; }
        public int CodigoUf { get; set; }
        public string CodigoPais { get; set; }
        public string Pais { get; set; }
        public string Telefone { get; set; }
    }
}
