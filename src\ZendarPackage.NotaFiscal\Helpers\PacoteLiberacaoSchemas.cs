﻿using System;
using System.Collections.Generic;
using ZendarPackage.NotaFiscal.Enums;

namespace ZendarPackage.NotaFiscal.Helpers
{
    public static class PacoteLiberacaoSchemas
    {
        private static readonly string NamespaceNFe = "http://www.portalfiscal.inf.br/nfe";
        private static readonly string VersaoNFe_4 = "4.00";
        public static readonly string VersaoNFeEvento_1 = "1.00";

        public static readonly Dictionary<string, string> NamespaceDeclaration = new() { { "", NamespaceNFe } };

        public static Dictionary<string, string> PreencherSchemas(ServicoNotaFiscal servico, string pl, string versao)
        {
            var pasta = $@"{AppDomain.CurrentDomain.BaseDirectory}Helpers\NotaFiscal\PacoteLiberacao\{pl}\";

            if (versao.Equals(VersaoNFe_4))
            {
                switch (servico)
                {
                    case ServicoNotaFiscal.INUTILIZACAO:
                        return new Dictionary<string, string>
                        {
                            { $"{pasta}inutNFe_v4.00.xsd", NamespaceNFe },
                            { $"{pasta}leiauteInutNFe_v4.00.xsd", NamespaceNFe },
                            { $"{pasta}tiposBasico_v4.00.xsd", NamespaceNFe },
                            { $"{pasta}xmldsig-core-schema_v1.01.xsd", "http://www.w3.org/2000/09/xmldsig#" },
                        };
                    case ServicoNotaFiscal.CONSULTA_PROTOCOLO:
                        return new Dictionary<string, string>
                        {
                            { $"{pasta}consSitNFe_v4.00.xsd", NamespaceNFe },
                            { $"{pasta}leiauteConsSitNFe_v4.00.xsd", NamespaceNFe },
                            { $"{pasta}tiposBasico_v4.00.xsd", NamespaceNFe },
                            { $"{pasta}xmldsig-core-schema_v1.01.xsd", "http://www.w3.org/2000/09/xmldsig#" },
                        };
                    case ServicoNotaFiscal.STATUS_SERVICO:
                        return new Dictionary<string, string>
                        {
                            { $"{pasta}consStatServ_v4.00.xsd", NamespaceNFe },
                            { $"{pasta}leiauteConsStatServ_v4.00.xsd", NamespaceNFe },
                            { $"{pasta}tiposBasico_v4.00.xsd", NamespaceNFe }
                        };
                    case ServicoNotaFiscal.RECEPCAO_EVENTO:
                        return new Dictionary<string, string>
                        {
                            { $"{pasta}envEvento_v1.00.xsd", NamespaceNFe },
                            { $"{pasta}leiauteEvento_v1.00.xsd", NamespaceNFe },
                            { $"{pasta}tiposBasico_v1.03.xsd", NamespaceNFe },
                            { $"{pasta}xmldsig-core-schema_v1.01.xsd", "http://www.w3.org/2000/09/xmldsig#" },
                        };
                    case ServicoNotaFiscal.PRE_AUTORIZACAO:
                        return new Dictionary<string, string>
                        {
                            { $"{pasta}nfe_v4.00.xsd", NamespaceNFe },
                            { $"{pasta}leiauteNFe_v4.00.xsd", NamespaceNFe },
                            { $"{pasta}tiposBasico_v4.00.xsd", NamespaceNFe },
                            { $"{pasta}xmldsig-core-schema_v1.01.xsd", "http://www.w3.org/2000/09/xmldsig#" }
                        };
                    case ServicoNotaFiscal.AUTORIZACAO:
                        return new Dictionary<string, string>
                        {
                            { $"{pasta}enviNFe_v4.00.xsd", NamespaceNFe },
                            { $"{pasta}leiauteNFe_v4.00.xsd", NamespaceNFe },
                            { $"{pasta}tiposBasico_v4.00.xsd", NamespaceNFe },
                            { $"{pasta}xmldsig-core-schema_v1.01.xsd", "http://www.w3.org/2000/09/xmldsig#" }
                        };
                    default:
                        break;
                }
            }

            return new Dictionary<string, string>();
        }
    }
}
