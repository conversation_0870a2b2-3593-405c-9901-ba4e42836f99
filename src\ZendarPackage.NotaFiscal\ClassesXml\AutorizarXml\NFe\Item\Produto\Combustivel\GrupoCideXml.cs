﻿using System.Xml.Serialization;
using ZendarPackage.NotaFiscal.Helpers.Formatadores;

namespace ZendarPackage.NotaFiscal.ClassesXml.AutorizarXml.NFe.Item.Produto.Combustivel
{
    [XmlRoot(ElementName = "CIDE")]
    public class GrupoCideXml
    {
        [XmlElement(ElementName = "qBCProd")]
        public string BaseCalculoCIDE { get; set; }

        [XmlElement(ElementName = "vAliqProd")]
        public string ValorAliquotaCIDE { get; set; }

        [XmlElement(ElementName = "vCIDE")]
        public string ValorCIDE { get; set; }

        public static GrupoCideXml ConverterXml(decimal baseCalculo, decimal aliquota, decimal valor)
        {
            if (baseCalculo == 0)
            {
                return null;
            }

            return new GrupoCideXml
            {
                BaseCalculoCIDE = FormatarValor.FormatarValorXmlNotaFiscal(baseCalculo, 4),
                ValorAliquotaCIDE = FormatarValor.FormatarValorXmlNotaFiscal(aliquota, 4),
                ValorCIDE = FormatarValor.FormatarValorXmlNotaFiscal(valor)
            };
        }
    }
}
